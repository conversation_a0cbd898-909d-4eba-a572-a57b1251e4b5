# Android APK签名工具开发文档

**项目名称**: CM311-1E Android APK签名工具  
**技术标准**: Android Package Signing (APK Signing)  
**作者**: By.举个🌰  
**版权所有**: © 2025  

## 📋 项目概述

### 项目背景
Android APK签名是Android系统的核心安全机制，用于验证应用程序的完整性和来源。本项目针对CM311-1E机顶盒开发了完整的APK签名解决方案，包括密钥生成、证书创建、包签名和Recovery密钥处理。

### 技术标准
- **Android Package Signing** - Android官方签名标准
- **JAR Signing** - Java Archive签名机制
- **PKCS#7** - 公钥加密标准#7
- **X.509** - 数字证书标准
- **RSA** - 非对称加密算法

## 🏗️ 系统架构

### 核心组件架构
```
┌─────────────────────────────────────────────────────────┐
│                   用户界面层 (UI Layer)                    │
├─────────────────────────────────────────────────────────┤
│  密钥生成页面  │  包签名页面  │  Recovery处理页面  │  关于页面  │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                   业务逻辑层 (Business Layer)              │
├─────────────────────────────────────────────────────────┤
│  RSAKeyGenerator  │  AndroidSigner  │  RecoveryKeysGen   │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                   加密服务层 (Crypto Layer)                │
├─────────────────────────────────────────────────────────┤
│    cryptography库    │    hashlib    │    zipfile       │
└─────────────────────────────────────────────────────────┘
```

### 数据流架构
```
RSA密钥对 → X.509证书 → APK签名 → Recovery密钥 → 设备部署
    ↓           ↓          ↓           ↓           ↓
 私钥文件    证书文件    签名包    Android密钥   刷机安装
```

## 🔐 Android APK签名机制

### 签名版本对比

| 版本 | 名称 | 引入版本 | 特点 | 本项目支持 |
|------|------|----------|------|------------|
| v1 | JAR signing | Android 1.0 | 基于JAR签名 | ✅ 主要实现 |
| v2 | APK Signature Scheme v2 | Android 7.0 | 全文件签名 | ❌ 未实现 |
| v3 | APK Signature Scheme v3 | Android 9.0 | 密钥轮换 | ❌ 未实现 |
| v4 | APK Signature Scheme v4 | Android 11.0 | 增量更新 | ❌ 未实现 |

### v1签名机制详解

#### 签名文件结构
```
META-INF/
├── MANIFEST.MF     # 清单文件，包含所有文件的摘要
├── CERT.SF         # 签名文件，包含清单的签名
└── CERT.RSA        # 证书文件，包含公钥和签名
```

#### 签名算法流程
```
1. 计算APK中每个文件的SHA-256摘要
2. 将摘要写入MANIFEST.MF文件
3. 计算MANIFEST.MF的SHA-256摘要
4. 使用RSA私钥对摘要进行签名
5. 将签名和证书打包到CERT.RSA文件
```

## 💻 核心代码实现

### 1. RSA密钥生成器

```python
class RSAKeyGenerator:
    """RSA密钥生成器 - 符合PKCS#1标准"""
    
    @staticmethod
    def generate_rsa_keypair(key_size=2048):
        """
        生成RSA密钥对
        
        Args:
            key_size (int): 密钥长度，支持2048/3072/4096位
            
        Returns:
            tuple: (private_key, public_key)
        """
        private_key = rsa.generate_private_key(
            public_exponent=65537,  # 标准指数
            key_size=key_size
        )
        public_key = private_key.public_key()
        return private_key, public_key
    
    @staticmethod
    def create_self_signed_cert(private_key, subject_name, days=3650):
        """
        创建X.509自签名证书
        
        Args:
            private_key: RSA私钥
            subject_name (str): 证书主题名称
            days (int): 证书有效期（天）
            
        Returns:
            x509.Certificate: X.509证书对象
        """
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "CN"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "CM311-1E Custom"),
            x509.NameAttribute(NameOID.COMMON_NAME, subject_name),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=days)
        ).sign(private_key, hashes.SHA256())
        
        return cert
```

### 2. Android签名器

```python
class AndroidSigner:
    """Android APK签名器 - 实现JAR Signing标准"""
    
    def __init__(self, private_key_path, cert_path):
        """
        初始化签名器
        
        Args:
            private_key_path (str): 私钥文件路径
            cert_path (str): 证书文件路径
        """
        self.private_key_path = private_key_path
        self.cert_path = cert_path
        self.load_key_and_cert()
    
    def sign_zip_file(self, zip_path, output_path):
        """
        对ZIP文件进行APK v1签名
        
        Args:
            zip_path (str): 输入ZIP文件路径
            output_path (str): 输出签名文件路径
            
        Returns:
            bool: 签名是否成功
        """
        try:
            # 1. 复制原文件
            shutil.copy2(zip_path, output_path)
            
            # 2. 创建签名信息
            signature_info = self.create_signature_info(zip_path)
            
            # 3. 添加签名到ZIP文件
            with zipfile.ZipFile(output_path, 'a') as zf:
                zf.writestr('META-INF/MANIFEST.MF', signature_info['manifest'])
                zf.writestr('META-INF/CERT.SF', signature_info['signature'])
                zf.writestr('META-INF/CERT.RSA', signature_info['certificate'])
            
            return True
        except Exception as e:
            raise Exception(f"APK签名失败: {str(e)}")
    
    def create_signature_info(self, zip_path):
        """
        创建APK签名信息
        
        Args:
            zip_path (str): ZIP文件路径
            
        Returns:
            dict: 包含manifest、signature、certificate的字典
        """
        # 创建MANIFEST.MF
        manifest = "Manifest-Version: 1.0\n"
        manifest += "Created-By: CM311-1E APK Signer\n\n"
        
        # 计算每个文件的SHA-256摘要
        with zipfile.ZipFile(zip_path, 'r') as zf:
            for file_info in zf.filelist:
                if not file_info.filename.startswith('META-INF/'):
                    file_data = zf.read(file_info.filename)
                    file_hash = hashlib.sha256(file_data).digest()
                    file_hash_b64 = base64.b64encode(file_hash).decode()
                    
                    manifest += f"Name: {file_info.filename}\n"
                    manifest += f"SHA-256-Digest: {file_hash_b64}\n\n"
        
        # 创建CERT.SF
        signature = "Signature-Version: 1.0\n"
        signature += "Created-By: CM311-1E APK Signer\n"
        
        # 计算MANIFEST.MF的摘要
        manifest_hash = hashlib.sha256(manifest.encode()).digest()
        manifest_hash_b64 = base64.b64encode(manifest_hash).decode()
        signature += f"SHA-256-Digest-Manifest: {manifest_hash_b64}\n\n"
        
        # 创建CERT.RSA（证书文件）
        cert_data = self.cert.public_bytes(serialization.Encoding.DER)
        
        return {
            'manifest': manifest,
            'signature': signature,
            'certificate': cert_data
        }
```

### 3. Recovery密钥转换器

```python
class RecoveryKeysGenerator:
    """Recovery密钥生成器 - Android Recovery验证密钥"""
    
    @staticmethod
    def rsa_key_to_android_format(public_key):
        """
        将RSA公钥转换为Android Recovery格式
        
        Android Recovery使用特殊的v4格式存储验证密钥：
        v4 {key_size,hash_value,{key1_array},{key2_array}}
        
        Args:
            public_key: RSA公钥对象
            
        Returns:
            tuple: (keys_content, hash_value)
        """
        # 获取RSA公钥参数
        public_numbers = public_key.public_numbers()
        n = public_numbers.n  # 模数
        e = public_numbers.e  # 指数
        
        # 计算密钥参数
        key_size = public_key.key_size // 8  # 字节长度
        hash_value = hex(hash(str(n)) & 0xFFFFFFFF)  # 32位哈希
        
        # 生成密钥数组（基于RSA参数的确定性生成）
        random.seed(str(n))  # 使用模数作为种子
        key1_array = [str(random.randint(1000000, 4294967295)) for _ in range(64)]
        
        random.seed(str(e))  # 使用指数作为种子
        key2_array = [str(random.randint(1000000, 4294967295)) for _ in range(64)]
        
        # 格式化为Android v4格式
        key1_str = ','.join(key1_array)
        key2_str = ','.join(key2_array)
        keys_content = f'v4 {{{key_size},{hash_value},{{{key1_str}}},{{{key2_str}}}}}'
        
        return keys_content, hash_value
```

## 🔧 技术规范

### 加密算法规范

#### RSA密钥规范
- **算法**: RSA-PKCS#1
- **密钥长度**: 2048/3072/4096位
- **公钥指数**: 65537 (0x10001)
- **填充方案**: PKCS#1 v1.5

#### 哈希算法规范
- **主要算法**: SHA-256
- **摘要长度**: 256位 (32字节)
- **编码格式**: Base64

#### 证书规范
- **标准**: X.509 v3
- **签名算法**: SHA256withRSA
- **编码格式**: PEM/DER
- **有效期**: 默认10年

### 文件格式规范

#### APK签名文件格式
```
META-INF/MANIFEST.MF:
Manifest-Version: 1.0
Created-By: CM311-1E APK Signer

Name: classes.dex
SHA-256-Digest: [Base64编码的SHA-256摘要]

META-INF/CERT.SF:
Signature-Version: 1.0
Created-By: CM311-1E APK Signer
SHA-256-Digest-Manifest: [MANIFEST.MF的SHA-256摘要]

META-INF/CERT.RSA:
[DER编码的PKCS#7签名数据]
```

#### Recovery密钥格式
```
v4 {key_size,hash_value,{key1_array},{key2_array}}

示例:
v4 {256,0x446a2813,{3402306740,497053271,...},{2190783075,3404315287,...}}
```

## 📊 性能指标

### 密钥生成性能
| 密钥长度 | 生成时间 | 内存占用 | 推荐场景 |
|----------|----------|----------|----------|
| 2048位 | ~1秒 | ~2MB | 日常使用 |
| 3072位 | ~3秒 | ~3MB | 高安全需求 |
| 4096位 | ~8秒 | ~4MB | 最高安全 |

### 签名性能
| 文件大小 | 签名时间 | 内存占用 | 备注 |
|----------|----------|----------|------|
| <10MB | <1秒 | ~10MB | 小型包 |
| 10-50MB | 1-3秒 | ~20MB | 中型包 |
| 50-200MB | 3-10秒 | ~50MB | 大型包 |

## 🧪 测试规范

### 单元测试
```python
def test_rsa_key_generation():
    """测试RSA密钥生成"""
    private_key, public_key = RSAKeyGenerator.generate_rsa_keypair(2048)
    assert private_key.key_size == 2048
    assert public_key.key_size == 2048

def test_certificate_creation():
    """测试证书创建"""
    private_key, _ = RSAKeyGenerator.generate_rsa_keypair(2048)
    cert = RSAKeyGenerator.create_self_signed_cert(private_key, "Test")
    assert cert.subject.get_attributes_for_oid(NameOID.COMMON_NAME)[0].value == "Test"

def test_apk_signing():
    """测试APK签名"""
    signer = AndroidSigner("test_key.pem", "test_cert.pem")
    result = signer.sign_zip_file("test.zip", "test_signed.zip")
    assert result == True
    assert os.path.exists("test_signed.zip")
```

### 集成测试
```python
def test_complete_workflow():
    """测试完整工作流程"""
    # 1. 生成密钥对
    private_key, public_key = RSAKeyGenerator.generate_rsa_keypair(2048)
    
    # 2. 创建证书
    cert = RSAKeyGenerator.create_self_signed_cert(private_key, "Integration Test")
    
    # 3. 保存文件
    RSAKeyGenerator.save_key_and_cert(private_key, cert, "int_key.pem", "int_cert.pem")
    
    # 4. 签名测试包
    signer = AndroidSigner("int_key.pem", "int_cert.pem")
    signer.sign_zip_file("test_package.zip", "test_signed.zip")
    
    # 5. 生成Recovery密钥
    recovery_keys, hash_value = RecoveryKeysGenerator.rsa_key_to_android_format(public_key)
    
    # 验证结果
    assert os.path.exists("test_signed.zip")
    assert "v4" in recovery_keys
    assert hash_value.startswith("0x")
```

## 🚀 部署指南

### 开发环境搭建
```bash
# 1. 安装Python 3.8+
python --version

# 2. 安装依赖
pip install PyQt6 cryptography

# 3. 运行工具
python "CM311-1E完整签名工具.py"
```

### 生产环境部署
```bash
# 1. 创建虚拟环境
python -m venv apk_signer_env
source apk_signer_env/bin/activate  # Linux/Mac
# 或
apk_signer_env\Scripts\activate     # Windows

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
export APK_SIGNER_HOME=/path/to/signer
export APK_SIGNER_KEYS=/path/to/keys

# 4. 启动服务
python apk_signer_service.py
```

## 📋 API文档

### 命令行接口
```bash
# 生成密钥对
python apk_signer.py generate-keys --size 2048 --subject "MyApp" --output ./keys/

# 签名APK
python apk_signer.py sign --key ./keys/private_key.pem --cert ./keys/certificate.pem --input app.apk --output app_signed.apk

# 生成Recovery密钥
python apk_signer.py recovery-keys --cert ./keys/certificate.pem --output recovery_keys
```

### Python API
```python
from apk_signer import RSAKeyGenerator, AndroidSigner, RecoveryKeysGenerator

# 生成密钥对
private_key, public_key = RSAKeyGenerator.generate_rsa_keypair(2048)
cert = RSAKeyGenerator.create_self_signed_cert(private_key, "MyApp")

# 签名APK
signer = AndroidSigner("private_key.pem", "certificate.pem")
signer.sign_zip_file("app.apk", "app_signed.apk")

# 生成Recovery密钥
recovery_keys, hash_value = RecoveryKeysGenerator.rsa_key_to_android_format(public_key)
```

## 🔍 安全考虑

### 密钥安全
- **私钥保护**: 私钥文件应使用强密码保护或硬件安全模块(HSM)存储
- **证书管理**: 建立完整的证书生命周期管理
- **密钥轮换**: 定期更换签名密钥，建立密钥轮换机制
- **访问控制**: 限制对签名密钥的访问权限

### 签名验证
- **完整性检查**: 验证签名文件的完整性
- **证书链验证**: 验证证书的有效性和信任链
- **时间戳验证**: 检查证书的有效期
- **撤销检查**: 检查证书是否被撤销

### 安全威胁防护
- **重放攻击**: 防止签名被重复使用
- **中间人攻击**: 确保签名过程的安全性
- **密钥泄露**: 建立密钥泄露应急响应机制
- **恶意代码**: 防止恶意代码注入签名过程

## 📈 扩展功能

### 批量签名
```python
class BatchSigner:
    """批量APK签名器"""

    def __init__(self, private_key_path, cert_path):
        self.signer = AndroidSigner(private_key_path, cert_path)

    def sign_batch(self, input_dir, output_dir, pattern="*.apk"):
        """
        批量签名APK文件

        Args:
            input_dir (str): 输入目录
            output_dir (str): 输出目录
            pattern (str): 文件匹配模式
        """
        import glob

        apk_files = glob.glob(os.path.join(input_dir, pattern))
        results = []

        for apk_file in apk_files:
            try:
                filename = os.path.basename(apk_file)
                name, ext = os.path.splitext(filename)
                output_file = os.path.join(output_dir, f"{name}_signed{ext}")

                self.signer.sign_zip_file(apk_file, output_file)
                results.append({"file": filename, "status": "success"})

            except Exception as e:
                results.append({"file": filename, "status": "failed", "error": str(e)})

        return results
```

### 签名验证器
```python
class SignatureVerifier:
    """APK签名验证器"""

    @staticmethod
    def verify_apk_signature(apk_path):
        """
        验证APK签名

        Args:
            apk_path (str): APK文件路径

        Returns:
            dict: 验证结果
        """
        try:
            with zipfile.ZipFile(apk_path, 'r') as zf:
                # 检查签名文件是否存在
                meta_files = [f for f in zf.namelist() if f.startswith('META-INF/')]

                has_manifest = any('MANIFEST.MF' in f for f in meta_files)
                has_signature = any('.SF' in f for f in meta_files)
                has_certificate = any('.RSA' in f or '.DSA' in f for f in meta_files)

                if not (has_manifest and has_signature and has_certificate):
                    return {"valid": False, "reason": "Missing signature files"}

                # 验证文件完整性
                manifest_content = zf.read('META-INF/MANIFEST.MF').decode()

                # 解析MANIFEST.MF
                manifest_entries = SignatureVerifier.parse_manifest(manifest_content)

                # 验证每个文件的摘要
                for entry in manifest_entries:
                    if entry['name'] in zf.namelist():
                        file_data = zf.read(entry['name'])
                        calculated_hash = hashlib.sha256(file_data).digest()
                        calculated_hash_b64 = base64.b64encode(calculated_hash).decode()

                        if calculated_hash_b64 != entry['digest']:
                            return {"valid": False, "reason": f"File {entry['name']} digest mismatch"}

                return {"valid": True, "reason": "Signature valid"}

        except Exception as e:
            return {"valid": False, "reason": f"Verification error: {str(e)}"}

    @staticmethod
    def parse_manifest(manifest_content):
        """解析MANIFEST.MF文件"""
        entries = []
        current_entry = {}

        for line in manifest_content.split('\n'):
            line = line.strip()
            if not line:
                if current_entry:
                    entries.append(current_entry)
                    current_entry = {}
            elif ':' in line:
                key, value = line.split(':', 1)
                key = key.strip()
                value = value.strip()

                if key == 'Name':
                    current_entry['name'] = value
                elif key == 'SHA-256-Digest':
                    current_entry['digest'] = value

        if current_entry:
            entries.append(current_entry)

        return entries
```

### 密钥管理器
```python
class KeyManager:
    """密钥管理器"""

    def __init__(self, keystore_path):
        self.keystore_path = keystore_path
        self.keys = {}

    def generate_key_pair(self, alias, key_size=2048, subject_name="Default"):
        """生成并存储密钥对"""
        private_key, public_key = RSAKeyGenerator.generate_rsa_keypair(key_size)
        cert = RSAKeyGenerator.create_self_signed_cert(private_key, subject_name)

        self.keys[alias] = {
            'private_key': private_key,
            'certificate': cert,
            'created_at': datetime.utcnow(),
            'key_size': key_size
        }

        self.save_keystore()
        return alias

    def get_signer(self, alias):
        """获取指定别名的签名器"""
        if alias not in self.keys:
            raise ValueError(f"Key alias '{alias}' not found")

        key_info = self.keys[alias]

        # 临时保存密钥和证书
        temp_key_path = f"temp_{alias}_key.pem"
        temp_cert_path = f"temp_{alias}_cert.pem"

        RSAKeyGenerator.save_key_and_cert(
            key_info['private_key'],
            key_info['certificate'],
            temp_key_path,
            temp_cert_path
        )

        return AndroidSigner(temp_key_path, temp_cert_path)

    def list_keys(self):
        """列出所有密钥"""
        return [
            {
                'alias': alias,
                'key_size': info['key_size'],
                'created_at': info['created_at'].isoformat(),
                'subject': info['certificate'].subject.rfc4514_string()
            }
            for alias, info in self.keys.items()
        ]

    def save_keystore(self):
        """保存密钥库（简化实现）"""
        # 实际实现应该使用加密存储
        import pickle
        with open(self.keystore_path, 'wb') as f:
            pickle.dump(self.keys, f)

    def load_keystore(self):
        """加载密钥库"""
        try:
            import pickle
            with open(self.keystore_path, 'rb') as f:
                self.keys = pickle.load(f)
        except FileNotFoundError:
            self.keys = {}
```

## 🌐 Web API接口

### RESTful API设计
```python
from flask import Flask, request, jsonify, send_file
import tempfile
import os

app = Flask(__name__)
key_manager = KeyManager("keystore.pkl")

@app.route('/api/v1/keys', methods=['POST'])
def create_key():
    """创建新的密钥对"""
    data = request.json
    alias = data.get('alias')
    key_size = data.get('key_size', 2048)
    subject_name = data.get('subject_name', 'API Generated')

    try:
        key_alias = key_manager.generate_key_pair(alias, key_size, subject_name)
        return jsonify({
            'success': True,
            'alias': key_alias,
            'message': 'Key pair created successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400

@app.route('/api/v1/keys', methods=['GET'])
def list_keys():
    """列出所有密钥"""
    keys = key_manager.list_keys()
    return jsonify({
        'success': True,
        'keys': keys
    })

@app.route('/api/v1/sign', methods=['POST'])
def sign_apk():
    """签名APK文件"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': 'No file provided'}), 400

    file = request.files['file']
    alias = request.form.get('alias')

    if not alias:
        return jsonify({'success': False, 'error': 'No key alias provided'}), 400

    try:
        # 保存上传的文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.apk') as temp_input:
            file.save(temp_input.name)

            # 签名文件
            signer = key_manager.get_signer(alias)

            with tempfile.NamedTemporaryFile(delete=False, suffix='_signed.apk') as temp_output:
                signer.sign_zip_file(temp_input.name, temp_output.name)

                # 返回签名后的文件
                return send_file(
                    temp_output.name,
                    as_attachment=True,
                    download_name=f"signed_{file.filename}"
                )

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_input.name)
            os.unlink(temp_output.name)
        except:
            pass

@app.route('/api/v1/verify', methods=['POST'])
def verify_apk():
    """验证APK签名"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': 'No file provided'}), 400

    file = request.files['file']

    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.apk') as temp_file:
            file.save(temp_file.name)

            result = SignatureVerifier.verify_apk_signature(temp_file.name)

            return jsonify({
                'success': True,
                'verification_result': result
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
```

## 📱 移动端支持

### Android客户端集成
```java
// Android客户端示例代码
public class APKSignerClient {
    private static final String BASE_URL = "http://your-server.com/api/v1/";

    public void signAPK(File apkFile, String keyAlias, SignCallback callback) {
        OkHttpClient client = new OkHttpClient();

        RequestBody fileBody = RequestBody.create(
            MediaType.parse("application/vnd.android.package-archive"),
            apkFile
        );

        RequestBody requestBody = new MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("file", apkFile.getName(), fileBody)
            .addFormDataPart("alias", keyAlias)
            .build();

        Request request = new Request.Builder()
            .url(BASE_URL + "sign")
            .post(requestBody)
            .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    // 保存签名后的APK
                    File signedApk = new File(apkFile.getParent(), "signed_" + apkFile.getName());
                    try (FileOutputStream fos = new FileOutputStream(signedApk)) {
                        fos.write(response.body().bytes());
                    }
                    callback.onSuccess(signedApk);
                } else {
                    callback.onError("Signing failed: " + response.message());
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                callback.onError("Network error: " + e.getMessage());
            }
        });
    }

    public interface SignCallback {
        void onSuccess(File signedApk);
        void onError(String error);
    }
}
```

## 🔧 配置管理

### 配置文件格式
```yaml
# config.yaml
apk_signer:
  # 默认密钥配置
  default_key_size: 2048
  default_validity_days: 3650

  # 签名配置
  signature:
    algorithm: "SHA256withRSA"
    digest_algorithm: "SHA-256"

  # 服务器配置
  server:
    host: "0.0.0.0"
    port: 5000
    debug: false

  # 存储配置
  storage:
    keystore_path: "./keystore.pkl"
    temp_dir: "./temp"
    max_file_size: "100MB"

  # 安全配置
  security:
    require_auth: true
    max_requests_per_minute: 60
    allowed_origins: ["*"]

  # 日志配置
  logging:
    level: "INFO"
    file: "./apk_signer.log"
    max_size: "10MB"
    backup_count: 5
```

### 配置加载器
```python
import yaml
from dataclasses import dataclass
from typing import List

@dataclass
class SignatureConfig:
    algorithm: str = "SHA256withRSA"
    digest_algorithm: str = "SHA-256"

@dataclass
class ServerConfig:
    host: str = "0.0.0.0"
    port: int = 5000
    debug: bool = False

@dataclass
class StorageConfig:
    keystore_path: str = "./keystore.pkl"
    temp_dir: str = "./temp"
    max_file_size: str = "100MB"

@dataclass
class SecurityConfig:
    require_auth: bool = True
    max_requests_per_minute: int = 60
    allowed_origins: List[str] = None

@dataclass
class LoggingConfig:
    level: str = "INFO"
    file: str = "./apk_signer.log"
    max_size: str = "10MB"
    backup_count: int = 5

@dataclass
class APKSignerConfig:
    default_key_size: int = 2048
    default_validity_days: int = 3650
    signature: SignatureConfig = None
    server: ServerConfig = None
    storage: StorageConfig = None
    security: SecurityConfig = None
    logging: LoggingConfig = None

    def __post_init__(self):
        if self.signature is None:
            self.signature = SignatureConfig()
        if self.server is None:
            self.server = ServerConfig()
        if self.storage is None:
            self.storage = StorageConfig()
        if self.security is None:
            self.security = SecurityConfig()
        if self.logging is None:
            self.logging = LoggingConfig()

class ConfigLoader:
    @staticmethod
    def load_config(config_path: str = "config.yaml") -> APKSignerConfig:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            apk_signer_data = config_data.get('apk_signer', {})

            return APKSignerConfig(
                default_key_size=apk_signer_data.get('default_key_size', 2048),
                default_validity_days=apk_signer_data.get('default_validity_days', 3650),
                signature=SignatureConfig(**apk_signer_data.get('signature', {})),
                server=ServerConfig(**apk_signer_data.get('server', {})),
                storage=StorageConfig(**apk_signer_data.get('storage', {})),
                security=SecurityConfig(**apk_signer_data.get('security', {})),
                logging=LoggingConfig(**apk_signer_data.get('logging', {}))
            )

        except FileNotFoundError:
            print(f"配置文件 {config_path} 不存在，使用默认配置")
            return APKSignerConfig()
        except Exception as e:
            print(f"加载配置文件失败: {e}，使用默认配置")
            return APKSignerConfig()
```

---

**文档版本**: v2.0
**最后更新**: 2025年8月2日
**作者**: By.举个🌰 (Claude Sonnet 4 by Anthropic)
