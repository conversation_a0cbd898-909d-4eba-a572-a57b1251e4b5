#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ramdisk 分析和解压工具
By.举个🌰
"""

import os
import gzip
import struct
import subprocess
import tempfile
import shutil
from pathlib import Path

def analyze_file_header(filepath):
    """分析文件头部，识别文件类型"""
    with open(filepath, 'rb') as f:
        header = f.read(16)
    
    print(f"文件大小: {os.path.getsize(filepath)} 字节")
    print(f"文件头部 (hex): {header.hex()}")
    print(f"文件头部 (ascii): {header}")
    
    # 检查常见的压缩格式
    if header.startswith(b'\x1f\x8b'):
        return 'gzip'
    elif header.startswith(b'BZh'):
        return 'bzip2'
    elif header.startswith(b'\x5d\x00\x00'):
        return 'lzma'
    elif header.startswith(b'\xfd7zXZ'):
        return 'xz'
    elif header.startswith(b'070701') or header.startswith(b'070702'):
        return 'cpio_newc'
    elif header.startswith(b'070707'):
        return 'cpio_old'
    else:
        return 'unknown'

def extract_gzip(input_file, output_file):
    """解压gzip文件"""
    try:
        with gzip.open(input_file, 'rb') as f_in:
            with open(output_file, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        return True
    except Exception as e:
        print(f"gzip解压失败: {e}")
        return False

def extract_cpio(input_file, output_dir):
    """解压cpio文件"""
    try:
        os.makedirs(output_dir, exist_ok=True)
        # 使用cpio命令解压
        cmd = f'cpio -idmv < "{input_file}"'
        result = subprocess.run(cmd, shell=True, cwd=output_dir, 
                              capture_output=True, text=True)
        if result.returncode == 0:
            return True
        else:
            print(f"cpio解压失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"cpio解压异常: {e}")
        return False

def try_multiple_extractions(filepath):
    """尝试多种解压方法"""
    base_name = Path(filepath).stem
    
    # 1. 尝试作为gzip解压
    print("\n=== 尝试gzip解压 ===")
    gzip_output = f"{base_name}_ungzip"
    if extract_gzip(filepath, gzip_output):
        print(f"gzip解压成功，输出文件: {gzip_output}")
        
        # 分析解压后的文件
        file_type = analyze_file_header(gzip_output)
        print(f"解压后文件类型: {file_type}")
        
        if file_type == 'cpio_newc' or file_type == 'cpio_old':
            print("\n=== 尝试cpio解压 ===")
            cpio_output_dir = f"{base_name}_extracted"
            if extract_cpio(gzip_output, cpio_output_dir):
                print(f"cpio解压成功，输出目录: {cpio_output_dir}")
                return cpio_output_dir
        
        return gzip_output
    
    # 2. 直接尝试作为cpio解压
    print("\n=== 直接尝试cpio解压 ===")
    cpio_output_dir = f"{base_name}_direct_cpio"
    if extract_cpio(filepath, cpio_output_dir):
        print(f"直接cpio解压成功，输出目录: {cpio_output_dir}")
        return cpio_output_dir
    
    print("所有解压方法都失败了")
    return None

def main():
    ramdisk_file = "ramdisk"
    
    if not os.path.exists(ramdisk_file):
        print(f"错误: 找不到文件 {ramdisk_file}")
        return
    
    print("=== Ramdisk 文件分析 ===")
    file_type = analyze_file_header(ramdisk_file)
    print(f"检测到的文件类型: {file_type}")
    
    print("\n=== 开始解压 ===")
    result = try_multiple_extractions(ramdisk_file)
    
    if result:
        print(f"\n✅ 解压成功！结果保存在: {result}")
        
        # 如果是目录，显示目录内容
        if os.path.isdir(result):
            print("\n📁 解压后的目录结构:")
            for root, dirs, files in os.walk(result):
                level = root.replace(result, '').count(os.sep)
                indent = ' ' * 2 * level
                print(f"{indent}{os.path.basename(root)}/")
                subindent = ' ' * 2 * (level + 1)
                for file in files[:10]:  # 只显示前10个文件
                    print(f"{subindent}{file}")
                if len(files) > 10:
                    print(f"{subindent}... 还有 {len(files) - 10} 个文件")
    else:
        print("\n❌ 解压失败")

if __name__ == "__main__":
    main()
