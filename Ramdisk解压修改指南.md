# Ramdisk 解压和修改完整指南

## 概述

本指南详细说明了如何解压、修改和重新打包Android设备的ramdisk文件。ramdisk是Android系统启动时加载的根文件系统，包含了系统的核心配置文件。

## 设备信息

通过分析发现，这是一个**CM311-1e**型号的Android TV盒子：
- **设备型号**: CM311-1e (IPTV机顶盒)
- **制造商**: CMDC
- **平台**: Amlogic S905L3
- **Android版本**: 9.0 (API Level 28)
- **构建日期**: 2024年6月13日

## 文件结构分析

### 原始ramdisk文件
- **文件名**: `ramdisk`
- **大小**: 7,409,404 字节 (约7.4MB)
- **格式**: gzip压缩的CPIO归档文件
- **文件头**: `1f8b0800` (gzip魔数)

### 解压后结构
解压后得到完整的Android根文件系统，包含：

```
ramdisk_extracted/
├── acct/                    # 进程统计目录
├── backup/                  # 备份目录
├── boot/                    # 启动相关
├── build_*.prop            # 各种设备型号的构建属性文件
├── cache/                   # 缓存目录
├── config/                  # 配置目录
├── data/                    # 数据目录
├── dev/                     # 设备文件目录
├── etc/                     # 配置文件
│   ├── recovery.fstab       # 恢复模式分区表
│   ├── remote.cfg           # 遥控器配置
│   └── ...
├── init                     # 初始化程序 (1.5MB)
├── init.rc                  # 初始化脚本
├── init.recovery.amlogic.rc # Amlogic恢复模式脚本
├── res/                     # 资源文件
│   └── images/              # 恢复模式界面图片
├── sbin/                    # 系统二进制文件
│   ├── adbd                 # ADB守护进程
│   ├── recovery             # 恢复模式程序
│   └── ...
├── sepolicy                 # SELinux策略文件
└── ...
```

## 工具脚本说明

### 1. ramdisk_analyzer.py
**功能**: 分析和初步解压ramdisk文件
- 识别文件格式（gzip压缩）
- 执行gzip解压，得到CPIO文件

### 2. cpio_extractor.py  
**功能**: 解压CPIO格式的文件系统
- 解析CPIO newc格式头部
- 提取所有文件和目录
- 处理符号链接（在Windows上创建.symlink标记文件）

### 3. demo_modify.py
**功能**: 演示如何修改配置文件
- 修改build.prop文件中的系统属性
- 启用调试功能

### 4. repack_ramdisk.py
**功能**: 重新打包修改后的文件系统
- 将目录重新打包为CPIO格式
- 使用gzip压缩生成新的ramdisk文件

## 使用步骤

### 第一步：解压ramdisk
```bash
python ramdisk_analyzer.py
python cpio_extractor.py
```

### 第二步：修改文件
可以修改 `ramdisk_extracted/` 目录中的任何文件，例如：

#### 修改系统属性
编辑 `build_CM311-1e.prop` 文件：
```properties
# 启用调试模式
ro.secure=0
ro.debuggable=1
ro.adb.secure=0

# 启用ADB
persist.service.adb.enable=1
persist.sys.usb.config=adb
```

#### 修改启动脚本
编辑 `init.rc` 或 `init.recovery.amlogic.rc` 文件来修改启动行为。

### 第三步：重新打包
```bash
python repack_ramdisk.py
```

生成的新文件：`ramdisk_modified`

## 常见修改示例

### 1. 启用ADB调试
```properties
ro.secure=0
ro.adb.secure=0
ro.debuggable=1
persist.service.adb.enable=1
persist.sys.usb.config=adb
```

### 2. 修改设备信息
```properties
ro.product.model=MyCustomDevice
ro.product.brand=MyBrand
ro.build.display.id=CustomROM
```

### 3. 启用Root权限
在 `init.rc` 中添加：
```bash
# 启用su命令
chmod 4755 /system/bin/su
```

## 注意事项

### ⚠️ 重要警告
1. **备份原文件**: 修改前务必备份原始ramdisk文件
2. **测试环境**: 建议先在测试设备上验证修改
3. **兼容性**: 确保修改不会破坏系统启动
4. **权限问题**: 某些修改可能需要解锁bootloader

### 🔧 技术细节
- **CPIO格式**: 使用newc格式 (070701魔数)
- **压缩算法**: gzip压缩
- **字节对齐**: 所有数据4字节对齐
- **文件权限**: 保持原始权限设置

### 📁 文件大小对比
- **原始文件**: 7,409,404 字节
- **修改后**: 7,401,622 字节  
- **差异**: -7,782 字节 (略小，正常现象)

## 故障排除

### 问题1: 解压失败
- 检查文件是否完整
- 确认文件格式是否正确

### 问题2: 重新打包后设备无法启动
- 检查关键文件是否存在
- 验证init脚本语法
- 恢复原始ramdisk文件

### 问题3: 修改不生效
- 确认修改了正确的配置文件
- 检查属性名称是否正确
- 验证文件权限

## 总结

通过本指南，你可以：
1. ✅ 成功解压ramdisk文件
2. ✅ 修改系统配置和属性
3. ✅ 重新打包生成新的ramdisk
4. ✅ 了解Android根文件系统结构

这为进一步的系统定制和逆向分析提供了基础。

---
**版权信息**: By.举个🌰  
**创建时间**: 2025年8月2日  
**适用设备**: CM311-1e及类似Android TV设备
