#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CPIO 文件解压工具
By.举个🌰
"""

import os
import struct
import stat
from pathlib import Path

class CPIOExtractor:
    def __init__(self):
        self.MAGIC_NEWC = b'070701'
        self.MAGIC_CRC = b'070702'
        
    def parse_newc_header(self, data, offset):
        """解析 CPIO newc 格式头部"""
        if offset + 110 > len(data):
            return None, offset
            
        header_data = data[offset:offset + 110]
        
        # 检查魔数
        if not header_data.startswith(self.MAGIC_NEWC) and not header_data.startswith(self.MAGIC_CRC):
            return None, offset
            
        try:
            # 解析头部字段 (所有字段都是8位十六进制ASCII)
            magic = header_data[0:6].decode('ascii')
            ino = int(header_data[6:14], 16)
            mode = int(header_data[14:22], 16)
            uid = int(header_data[22:30], 16)
            gid = int(header_data[30:38], 16)
            nlink = int(header_data[38:46], 16)
            mtime = int(header_data[46:54], 16)
            filesize = int(header_data[54:62], 16)
            devmajor = int(header_data[62:70], 16)
            devminor = int(header_data[70:78], 16)
            rdevmajor = int(header_data[78:86], 16)
            rdevminor = int(header_data[86:94], 16)
            namesize = int(header_data[94:102], 16)
            check = int(header_data[102:110], 16)
            
            header = {
                'magic': magic,
                'ino': ino,
                'mode': mode,
                'uid': uid,
                'gid': gid,
                'nlink': nlink,
                'mtime': mtime,
                'filesize': filesize,
                'devmajor': devmajor,
                'devminor': devminor,
                'rdevmajor': rdevmajor,
                'rdevminor': rdevminor,
                'namesize': namesize,
                'check': check
            }
            
            return header, offset + 110
            
        except ValueError as e:
            print(f"头部解析错误: {e}")
            return None, offset
    
    def extract_cpio(self, input_file, output_dir):
        """解压CPIO文件"""
        print(f"开始解压 {input_file} 到 {output_dir}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        with open(input_file, 'rb') as f:
            data = f.read()
        
        offset = 0
        file_count = 0
        
        while offset < len(data):
            # 解析头部
            header, new_offset = self.parse_newc_header(data, offset)
            if header is None:
                print(f"在偏移 {offset} 处无法解析头部，停止解压")
                break
                
            offset = new_offset
            
            # 读取文件名
            if offset + header['namesize'] > len(data):
                print("文件名超出数据范围")
                break
                
            filename_data = data[offset:offset + header['namesize']]
            # 移除末尾的null字节
            filename = filename_data.rstrip(b'\x00').decode('utf-8', errors='ignore')
            offset += header['namesize']
            
            # 对齐到4字节边界
            offset = (offset + 3) & ~3
            
            # 检查是否是结束标记
            if filename == 'TRAILER!!!':
                print("遇到结束标记，解压完成")
                break
            
            print(f"处理文件: {filename} (大小: {header['filesize']} 字节)")
            
            # 创建完整路径
            full_path = os.path.join(output_dir, filename.lstrip('/'))
            dir_path = os.path.dirname(full_path)
            
            # 创建目录
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)
            
            # 根据文件类型处理
            file_mode = header['mode']
            
            if stat.S_ISDIR(file_mode):
                # 目录
                os.makedirs(full_path, exist_ok=True)
                print(f"  创建目录: {filename}")
                
            elif stat.S_ISREG(file_mode):
                # 普通文件
                if offset + header['filesize'] > len(data):
                    print(f"文件数据超出范围: {filename}")
                    break
                    
                file_data = data[offset:offset + header['filesize']]
                
                try:
                    with open(full_path, 'wb') as out_file:
                        out_file.write(file_data)
                    print(f"  提取文件: {filename}")
                    file_count += 1
                except Exception as e:
                    print(f"  写入文件失败 {filename}: {e}")
                
                offset += header['filesize']
                
            elif stat.S_ISLNK(file_mode):
                # 符号链接
                if offset + header['filesize'] > len(data):
                    print(f"链接数据超出范围: {filename}")
                    break
                    
                link_target = data[offset:offset + header['filesize']].decode('utf-8', errors='ignore')
                print(f"  符号链接: {filename} -> {link_target}")
                
                # 在Windows上创建符号链接可能需要管理员权限，这里只记录
                try:
                    # 创建一个文本文件记录链接信息
                    with open(full_path + '.symlink', 'w') as f:
                        f.write(f"SYMLINK_TARGET: {link_target}\n")
                except Exception as e:
                    print(f"  记录符号链接失败: {e}")
                
                offset += header['filesize']
                
            else:
                # 其他类型文件（设备文件等）
                print(f"  跳过特殊文件: {filename} (模式: {oct(file_mode)})")
                offset += header['filesize']
            
            # 对齐到4字节边界
            offset = (offset + 3) & ~3
        
        print(f"\n解压完成！共处理 {file_count} 个文件")
        return True

def main():
    extractor = CPIOExtractor()
    
    # 检查解压后的gzip文件
    cpio_file = "ramdisk_ungzip"
    output_dir = "ramdisk_extracted"
    
    if not os.path.exists(cpio_file):
        print(f"错误: 找不到文件 {cpio_file}")
        print("请先运行 ramdisk_analyzer.py 来解压gzip文件")
        return
    
    print("=== CPIO 文件解压 ===")
    success = extractor.extract_cpio(cpio_file, output_dir)
    
    if success:
        print(f"\n✅ 解压成功！文件保存在: {output_dir}")
        
        # 显示目录结构
        print("\n📁 解压后的目录结构:")
        for root, dirs, files in os.walk(output_dir):
            level = root.replace(output_dir, '').count(os.sep)
            indent = '  ' * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = '  ' * (level + 1)
            for file in files[:20]:  # 显示前20个文件
                print(f"{subindent}{file}")
            if len(files) > 20:
                print(f"{subindent}... 还有 {len(files) - 20} 个文件")
    else:
        print("\n❌ 解压失败")

if __name__ == "__main__":
    main()
