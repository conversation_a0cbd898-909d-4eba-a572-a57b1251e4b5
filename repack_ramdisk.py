#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新打包ramdisk工具
By.举个🌰
"""

import os
import gzip
import struct
import stat
import time
from pathlib import Path

class CPIOPacker:
    def __init__(self):
        self.MAGIC_NEWC = '070701'
        
    def create_header(self, filename, file_mode, file_size, mtime=None, ino=1, uid=0, gid=0, nlink=1):
        """创建CPIO newc格式头部"""
        if mtime is None:
            mtime = int(time.time())
            
        # 所有字段都是8位十六进制ASCII
        header = self.MAGIC_NEWC
        header += f"{ino:08x}"                   # inode
        header += f"{file_mode:08x}"             # mode
        header += f"{uid:08x}"                   # uid
        header += f"{gid:08x}"                   # gid
        header += f"{nlink:08x}"                 # nlink
        header += f"{mtime:08x}"                 # mtime
        header += f"{file_size:08x}"             # filesize
        header += f"{0:08x}"                     # devmajor
        header += f"{0:08x}"                     # devminor
        header += f"{0:08x}"                     # rdevmajor
        header += f"{0:08x}"                     # rdevminor
        header += f"{len(filename) + 1:08x}"     # namesize (包括null终止符)
        header += f"{0:08x}"                     # check
        
        return header.encode('ascii')
    
    def pad_to_4_bytes(self, data):
        """填充到4字节对齐"""
        padding = (4 - len(data) % 4) % 4
        return data + b'\x00' * padding
    
    def pack_directory(self, input_dir, output_file):
        """将目录打包为CPIO格式"""
        print(f"开始打包目录 {input_dir} 到 {output_file}")
        
        file_list = []
        ino_counter = 1
        
        # 收集所有文件和目录
        for root, dirs, files in os.walk(input_dir):
            # 添加目录
            for dirname in sorted(dirs):
                dir_path = os.path.join(root, dirname)
                rel_path = os.path.relpath(dir_path, input_dir).replace('\\', '/')
                
                try:
                    dir_stat = os.stat(dir_path)
                    file_list.append({
                        'path': rel_path,
                        'full_path': dir_path,
                        'mode': stat.S_IFDIR | 0o755,  # 目录权限
                        'size': 0,
                        'mtime': int(dir_stat.st_mtime),
                        'ino': ino_counter,
                        'type': 'dir'
                    })
                    ino_counter += 1
                except Exception as e:
                    print(f"警告: 无法获取目录状态 {dir_path}: {e}")
            
            # 添加文件
            for filename in sorted(files):
                file_path = os.path.join(root, filename)
                rel_path = os.path.relpath(file_path, input_dir).replace('\\', '/')
                
                # 跳过符号链接标记文件
                if filename.endswith('.symlink'):
                    continue
                
                try:
                    file_stat = os.stat(file_path)
                    file_list.append({
                        'path': rel_path,
                        'full_path': file_path,
                        'mode': stat.S_IFREG | 0o644,  # 普通文件权限
                        'size': file_stat.st_size,
                        'mtime': int(file_stat.st_mtime),
                        'ino': ino_counter,
                        'type': 'file'
                    })
                    ino_counter += 1
                except Exception as e:
                    print(f"警告: 无法获取文件状态 {file_path}: {e}")
        
        # 按路径排序
        file_list.sort(key=lambda x: x['path'])
        
        # 写入CPIO文件
        with open(output_file, 'wb') as f:
            for item in file_list:
                # 创建头部
                header = self.create_header(
                    item['path'], 
                    item['mode'], 
                    item['size'], 
                    item['mtime'], 
                    item['ino']
                )
                f.write(header)
                
                # 写入文件名（包括null终止符）
                filename_data = (item['path'] + '\x00').encode('utf-8')
                f.write(self.pad_to_4_bytes(filename_data))
                
                # 写入文件内容（如果是文件）
                if item['type'] == 'file':
                    try:
                        with open(item['full_path'], 'rb') as file_in:
                            file_data = file_in.read()
                            f.write(self.pad_to_4_bytes(file_data))
                        print(f"  打包文件: {item['path']} ({item['size']} 字节)")
                    except Exception as e:
                        print(f"错误: 无法读取文件 {item['full_path']}: {e}")
                else:
                    print(f"  打包目录: {item['path']}")
            
            # 写入结束标记
            trailer_header = self.create_header('TRAILER!!!', 0, 0, int(time.time()), ino_counter)
            f.write(trailer_header)
            trailer_name = b'TRAILER!!!\x00'
            f.write(self.pad_to_4_bytes(trailer_name))
            
            print("  添加结束标记")
        
        print(f"打包完成: {output_file}")
        return True

def compress_gzip(input_file, output_file):
    """使用gzip压缩文件"""
    print(f"开始gzip压缩 {input_file} 到 {output_file}")
    
    try:
        with open(input_file, 'rb') as f_in:
            with gzip.open(output_file, 'wb') as f_out:
                f_out.write(f_in.read())
        
        print(f"压缩完成: {output_file}")
        return True
    except Exception as e:
        print(f"压缩失败: {e}")
        return False

def main():
    extracted_dir = "ramdisk_extracted"
    
    if not os.path.exists(extracted_dir):
        print(f"错误: 找不到解压目录 {extracted_dir}")
        print("请先运行解压脚本")
        return
    
    print("=== 重新打包ramdisk ===")
    
    # 创建打包器
    packer = CPIOPacker()
    
    # 打包为CPIO
    cpio_file = "ramdisk_modified.cpio"
    print(f"步骤1: 打包为CPIO格式...")
    if packer.pack_directory(extracted_dir, cpio_file):
        
        # 压缩为gzip
        output_ramdisk = "ramdisk_modified"
        print(f"\n步骤2: 压缩为gzip格式...")
        if compress_gzip(cpio_file, output_ramdisk):
            print(f"\n✅ 重新打包完成！")
            print(f"新的ramdisk文件: {output_ramdisk}")
            
            # 显示文件大小对比
            if os.path.exists('ramdisk'):
                original_size = os.path.getsize('ramdisk')
                modified_size = os.path.getsize(output_ramdisk)
                print(f"原始文件大小: {original_size:,} 字节")
                print(f"修改后文件大小: {modified_size:,} 字节")
                print(f"大小差异: {modified_size - original_size:+,} 字节")
            
            # 清理临时文件
            try:
                os.remove(cpio_file)
                print(f"清理临时文件: {cpio_file}")
            except:
                pass
                
            print(f"\n🎉 成功！你现在可以使用 {output_ramdisk} 替换原始的ramdisk文件")
        else:
            print("❌ gzip压缩失败")
    else:
        print("❌ CPIO打包失败")

if __name__ == "__main__":
    main()
