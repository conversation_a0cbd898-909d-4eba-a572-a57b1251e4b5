#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ramdisk 文件修改和重新打包工具
By.举个🌰
"""

import os
import gzip
import struct
import stat
import time
from pathlib import Path

class CPIOPacker:
    def __init__(self):
        self.MAGIC_NEWC = '070701'
        
    def create_header(self, filename, file_stat, filesize=0):
        """创建CPIO newc格式头部"""
        # 所有字段都是8位十六进制ASCII
        header = self.MAGIC_NEWC
        header += f"{file_stat.st_ino:08x}"      # inode
        header += f"{file_stat.st_mode:08x}"     # mode
        header += f"{file_stat.st_uid:08x}"      # uid
        header += f"{file_stat.st_gid:08x}"      # gid
        header += f"{file_stat.st_nlink:08x}"    # nlink
        header += f"{int(file_stat.st_mtime):08x}"  # mtime
        header += f"{filesize:08x}"              # filesize
        header += f"{0:08x}"                     # devmajor
        header += f"{0:08x}"                     # devminor
        header += f"{0:08x}"                     # rdevmajor
        header += f"{0:08x}"                     # rdevminor
        header += f"{len(filename) + 1:08x}"     # namesize (包括null终止符)
        header += f"{0:08x}"                     # check
        
        return header.encode('ascii')
    
    def pad_to_4_bytes(self, data):
        """填充到4字节对齐"""
        padding = (4 - len(data) % 4) % 4
        return data + b'\x00' * padding
    
    def pack_directory(self, input_dir, output_file):
        """将目录打包为CPIO格式"""
        print(f"开始打包目录 {input_dir} 到 {output_file}")
        
        with open(output_file, 'wb') as f:
            # 遍历所有文件和目录
            for root, dirs, files in os.walk(input_dir):
                # 处理目录
                for dirname in sorted(dirs):
                    dir_path = os.path.join(root, dirname)
                    rel_path = os.path.relpath(dir_path, input_dir)
                    
                    # 获取目录状态
                    dir_stat = os.stat(dir_path)
                    
                    # 创建头部
                    header = self.create_header(rel_path, dir_stat, 0)
                    f.write(header)
                    
                    # 写入文件名（包括null终止符）
                    filename_data = (rel_path + '\x00').encode('utf-8')
                    f.write(self.pad_to_4_bytes(filename_data))
                    
                    print(f"  打包目录: {rel_path}")
                
                # 处理文件
                for filename in sorted(files):
                    file_path = os.path.join(root, filename)
                    rel_path = os.path.relpath(file_path, input_dir)
                    
                    # 跳过符号链接标记文件
                    if filename.endswith('.symlink'):
                        continue
                    
                    # 获取文件状态
                    file_stat = os.stat(file_path)
                    file_size = file_stat.st_size
                    
                    # 创建头部
                    header = self.create_header(rel_path, file_stat, file_size)
                    f.write(header)
                    
                    # 写入文件名（包括null终止符）
                    filename_data = (rel_path + '\x00').encode('utf-8')
                    f.write(self.pad_to_4_bytes(filename_data))
                    
                    # 写入文件内容
                    with open(file_path, 'rb') as file_in:
                        file_data = file_in.read()
                        f.write(self.pad_to_4_bytes(file_data))
                    
                    print(f"  打包文件: {rel_path} ({file_size} 字节)")
            
            # 写入结束标记
            trailer_stat = os.stat(input_dir)  # 使用输入目录的状态
            trailer_header = self.create_header('TRAILER!!!', trailer_stat, 0)
            f.write(trailer_header)
            trailer_name = b'TRAILER!!!\x00'
            f.write(self.pad_to_4_bytes(trailer_name))
            
            print("  添加结束标记")
        
        print(f"打包完成: {output_file}")
        return True

def compress_gzip(input_file, output_file):
    """使用gzip压缩文件"""
    print(f"开始gzip压缩 {input_file} 到 {output_file}")
    
    with open(input_file, 'rb') as f_in:
        with gzip.open(output_file, 'wb') as f_out:
            f_out.write(f_in.read())
    
    print(f"压缩完成: {output_file}")
    return True

def modify_build_prop(prop_file, modifications):
    """修改build.prop文件"""
    print(f"修改文件: {prop_file}")
    
    if not os.path.exists(prop_file):
        print(f"文件不存在: {prop_file}")
        return False
    
    # 读取原文件
    with open(prop_file, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()
    
    # 应用修改
    modified_lines = []
    modified_props = set()
    
    for line in lines:
        line = line.rstrip('\n\r')
        modified = False
        
        for prop_name, new_value in modifications.items():
            if line.startswith(f"{prop_name}=") or line.startswith(f"#{prop_name}="):
                modified_lines.append(f"{prop_name}={new_value}\n")
                modified_props.add(prop_name)
                modified = True
                print(f"  修改: {prop_name}={new_value}")
                break
        
        if not modified:
            modified_lines.append(line + '\n')
    
    # 添加新属性（如果不存在）
    for prop_name, new_value in modifications.items():
        if prop_name not in modified_props:
            modified_lines.append(f"{prop_name}={new_value}\n")
            print(f"  添加: {prop_name}={new_value}")
    
    # 写回文件
    with open(prop_file, 'w', encoding='utf-8') as f:
        f.writelines(modified_lines)
    
    print(f"文件修改完成: {prop_file}")
    return True

def main():
    extracted_dir = "ramdisk_extracted"
    
    if not os.path.exists(extracted_dir):
        print(f"错误: 找不到解压目录 {extracted_dir}")
        print("请先运行 ramdisk_analyzer.py 和 cpio_extractor.py")
        return
    
    print("=== Ramdisk 修改工具 ===")
    print("1. 修改文件")
    print("2. 重新打包ramdisk")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-3): ").strip()
        
        if choice == '1':
            print("\n=== 文件修改 ===")
            print("示例修改build.prop文件...")
            
            # 示例：修改一些属性
            modifications = {
                'ro.debuggable': '1',
                'ro.secure': '0',
                'ro.adb.secure': '0',
                'persist.service.adb.enable': '1',
                'persist.service.debuggable': '1',
                'persist.sys.usb.config': 'adb'
            }
            
            # 修改所有build.prop文件
            for filename in os.listdir(extracted_dir):
                if filename.startswith('build_') and filename.endswith('.prop'):
                    prop_file = os.path.join(extracted_dir, filename)
                    modify_build_prop(prop_file, modifications)
            
            # 也可以修改其他文件
            print("\n你可以手动编辑 ramdisk_extracted 目录中的任何文件")
            print("修改完成后选择选项2重新打包")
            
        elif choice == '2':
            print("\n=== 重新打包ramdisk ===")
            
            # 创建打包器
            packer = CPIOPacker()
            
            # 打包为CPIO
            cpio_file = "ramdisk_modified.cpio"
            if packer.pack_directory(extracted_dir, cpio_file):
                
                # 压缩为gzip
                output_ramdisk = "ramdisk_modified"
                if compress_gzip(cpio_file, output_ramdisk):
                    print(f"\n✅ 重新打包完成！")
                    print(f"新的ramdisk文件: {output_ramdisk}")
                    print(f"原始文件大小: {os.path.getsize('ramdisk')} 字节")
                    print(f"修改后文件大小: {os.path.getsize(output_ramdisk)} 字节")
                    
                    # 清理临时文件
                    os.remove(cpio_file)
                else:
                    print("❌ gzip压缩失败")
            else:
                print("❌ CPIO打包失败")
                
        elif choice == '3':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
