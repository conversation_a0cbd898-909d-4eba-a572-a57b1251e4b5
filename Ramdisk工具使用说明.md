# Ramdisk 解包打包工具使用说明

## 概述

这是一个基于CustomTkinter的现代化Ramdisk解包打包工具，支持Android设备的ramdisk文件解包和重新打包。

## 功能特性

### 🎯 主要功能
- **解包功能**: 解包gzip压缩的CPIO格式ramdisk文件
- **打包功能**: 将目录重新打包为ramdisk文件
- **文件分析**: 自动识别文件格式和显示详细信息
- **现代化UI**: 基于CustomTkinter的暗色主题界面

### 📋 支持格式
- gzip + CPIO newc格式
- Android ramdisk镜像文件
- 自动识别文件类型和魔数

### ⚡ 特色功能
- 实时进度显示
- 详细操作日志
- 文件/目录信息分析
- 一键打开目录/文件
- 多线程处理，界面不卡顿

## 界面介绍

### 📦 解包选项卡
1. **文件选择**: 选择要解包的ramdisk文件
2. **输出目录**: 选择解包后文件的保存位置
3. **文件信息**: 显示选中文件的详细信息
4. **操作按钮**: 开始解包、打开目录
5. **进度条**: 显示解包进度
6. **操作日志**: 显示详细的操作过程

### 📁 打包选项卡
1. **目录选择**: 选择要打包的目录
2. **输出文件**: 选择打包后ramdisk文件的保存位置
3. **目录信息**: 显示选中目录的详细信息
4. **操作按钮**: 开始打包、打开文件
5. **进度条**: 显示打包进度
6. **操作日志**: 显示详细的操作过程

### ℹ️ 关于选项卡
- 显示工具版本信息
- 功能特性说明
- 版权信息

## 使用步骤

### 解包操作

1. **选择文件**
   - 点击"浏览"按钮选择ramdisk文件
   - 支持的文件类型：ramdisk、*.img、*.gz等
   - 工具会自动分析文件格式和大小

2. **设置输出目录**
   - 工具会自动设置输出目录（文件名_extracted）
   - 也可以手动选择其他目录

3. **查看文件信息**
   - 文件路径、大小、类型
   - 魔数、修改时间等详细信息

4. **开始解包**
   - 点击"🚀 开始解包"按钮
   - 观察进度条和日志信息
   - 解包完成后可点击"📂 打开目录"

### 打包操作

1. **选择目录**
   - 点击"浏览"按钮选择要打包的目录
   - 通常是之前解包得到的目录

2. **设置输出文件**
   - 工具会自动设置输出文件名（目录名_packed）
   - 也可以手动指定文件名和路径

3. **查看目录信息**
   - 目录路径、文件数量、总大小
   - 修改时间等详细信息

4. **开始打包**
   - 点击"📦 开始打包"按钮
   - 观察进度条和日志信息
   - 打包完成后可点击"📁 打开文件"

## 技术细节

### 文件格式支持
- **gzip压缩**: 识别魔数 `1f8b`
- **CPIO newc**: 识别魔数 `070701`
- **CPIO newc CRC**: 识别魔数 `070702`

### 处理流程

#### 解包流程
1. 分析文件头部，识别格式
2. 使用gzip解压外层压缩
3. 解析CPIO newc格式头部
4. 提取文件和目录结构
5. 处理符号链接（创建.symlink文件）

#### 打包流程
1. 遍历目录结构，收集文件信息
2. 按路径排序，分配inode编号
3. 创建CPIO newc格式头部
4. 写入文件内容，4字节对齐
5. 添加TRAILER!!!结束标记
6. 使用gzip压缩最终文件

### 文件权限处理
- **目录权限**: 0o755 (rwxr-xr-x)
- **文件权限**: 0o644 (rw-r--r--)
- **保持原始时间戳**

## 常见问题

### Q: 支持哪些文件格式？
A: 主要支持gzip压缩的CPIO newc格式，这是Android ramdisk的标准格式。

### Q: 解包后的符号链接如何处理？
A: 在Windows环境下，符号链接会被保存为.symlink文件，内容记录链接目标。

### Q: 打包后的文件大小为什么不同？
A: 这是正常现象，因为：
- 压缩算法的差异
- 文件对齐方式的不同
- 时间戳的更新

### Q: 程序卡住不动怎么办？
A: 程序使用多线程处理，界面应该保持响应。如果真的卡住，可以：
- 检查文件是否被其他程序占用
- 确认磁盘空间是否充足
- 重启程序重试

### Q: 如何修改解包后的文件？
A: 解包后可以：
- 直接编辑文本文件（如build.prop、init.rc）
- 替换二进制文件
- 修改目录结构
- 然后重新打包

## 注意事项

### ⚠️ 重要提醒
1. **备份原文件**: 操作前务必备份原始ramdisk文件
2. **权限问题**: 某些系统文件可能需要管理员权限
3. **路径长度**: Windows系统注意路径长度限制
4. **磁盘空间**: 确保有足够的磁盘空间进行操作

### 🔧 最佳实践
1. **测试环境**: 建议先在测试设备上验证修改
2. **小步修改**: 每次只修改少量内容，便于排错
3. **记录变更**: 记录所做的修改，便于回滚
4. **验证完整性**: 打包后检查文件大小和格式

## 技术支持

如果遇到问题，请检查：
1. 文件格式是否正确
2. 路径中是否包含特殊字符
3. 磁盘空间是否充足
4. 文件是否被其他程序占用

---

**版权信息**: By.举个🌰  
**创建时间**: 2025年8月2日  
**工具版本**: 1.0.0  
**适用平台**: Windows 10/11  
**Python版本**: 3.7+
