#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ramdisk 解包打包工具 - CustomTkinter版本
支持解包和重新打包ramdisk文件
By.举个🌰
"""

import os
import sys
import gzip
import stat
import time
import threading
import tkinter as tk
from tkinter import filedialog, messagebox
from pathlib import Path

try:
    import customtkinter as ctk
except ImportError:
    print("缺少CustomTkinter依赖，请运行: pip install customtkinter")
    sys.exit(1)

# 设置CustomTkinter主题
ctk.set_appearance_mode("dark")  # 可选: "light", "dark", "system"
ctk.set_default_color_theme("blue")  # 可选: "blue", "green", "dark-blue"

class CPIOHandler:
    """CPIO文件处理类"""
    
    def __init__(self):
        self.MAGIC_NEWC = b'070701'
        self.MAGIC_CRC = b'070702'
        
    def parse_newc_header(self, data, offset):
        """解析CPIO newc格式头部"""
        if offset + 110 > len(data):
            return None, offset
            
        header_data = data[offset:offset + 110]
        
        if not header_data.startswith(self.MAGIC_NEWC) and not header_data.startswith(self.MAGIC_CRC):
            return None, offset
            
        try:
            # 解析头部字段
            magic = header_data[0:6].decode('ascii')
            ino = int(header_data[6:14], 16)
            mode = int(header_data[14:22], 16)
            uid = int(header_data[22:30], 16)
            gid = int(header_data[30:38], 16)
            nlink = int(header_data[38:46], 16)
            mtime = int(header_data[46:54], 16)
            filesize = int(header_data[54:62], 16)
            devmajor = int(header_data[62:70], 16)
            devminor = int(header_data[70:78], 16)
            rdevmajor = int(header_data[78:86], 16)
            rdevminor = int(header_data[86:94], 16)
            namesize = int(header_data[94:102], 16)
            check = int(header_data[102:110], 16)
            
            return {
                'magic': magic, 'ino': ino, 'mode': mode, 'uid': uid, 'gid': gid,
                'nlink': nlink, 'mtime': mtime, 'filesize': filesize,
                'devmajor': devmajor, 'devminor': devminor, 'rdevmajor': rdevmajor,
                'rdevminor': rdevminor, 'namesize': namesize, 'check': check
            }, offset + 110
            
        except ValueError:
            return None, offset
    
    def create_header(self, filename, file_mode, file_size, mtime=None, ino=1, uid=0, gid=0, nlink=1):
        """创建CPIO newc格式头部"""
        if mtime is None:
            mtime = int(time.time())
            
        header = '070701'  # MAGIC_NEWC
        header += f"{ino:08x}"
        header += f"{file_mode:08x}"
        header += f"{uid:08x}"
        header += f"{gid:08x}"
        header += f"{nlink:08x}"
        header += f"{mtime:08x}"
        header += f"{file_size:08x}"
        header += f"{0:08x}"  # devmajor
        header += f"{0:08x}"  # devminor
        header += f"{0:08x}"  # rdevmajor
        header += f"{0:08x}"  # rdevminor
        header += f"{len(filename) + 1:08x}"  # namesize
        header += f"{0:08x}"  # check
        
        return header.encode('ascii')
    
    def pad_to_4_bytes(self, data):
        """填充到4字节对齐"""
        padding = (4 - len(data) % 4) % 4
        return data + b'\x00' * padding

class RamdiskTool:
    """Ramdisk工具主类"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Ramdisk 解包打包工具 - By.举个🌰")
        self.root.geometry("1000x700")
        self.root.minsize(900, 650)
        
        # 设置窗口图标
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        self.cpio_handler = CPIOHandler()
        self.current_operation = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_label = ctk.CTkLabel(
            self.root,
            text="🔧 Ramdisk 解包打包工具",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # 创建主框架
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # 创建选项卡
        self.tabview = ctk.CTkTabview(main_frame)
        self.tabview.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 解包选项卡
        self.extract_tab = self.tabview.add("📦 解包")
        self.setup_extract_tab()
        
        # 打包选项卡
        self.pack_tab = self.tabview.add("📁 打包")
        self.setup_pack_tab()
        
        # 关于选项卡
        self.about_tab = self.tabview.add("ℹ️ 关于")
        self.setup_about_tab()
        
    def setup_extract_tab(self):
        """设置解包选项卡"""
        # 文件选择框架
        file_frame = ctk.CTkFrame(self.extract_tab)
        file_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(file_frame, text="选择Ramdisk文件:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        file_select_frame = ctk.CTkFrame(file_frame)
        file_select_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        self.extract_file_entry = ctk.CTkEntry(file_select_frame, placeholder_text="请选择ramdisk文件...")
        self.extract_file_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)
        
        self.extract_browse_btn = ctk.CTkButton(
            file_select_frame,
            text="浏览",
            width=80,
            command=self.browse_extract_file
        )
        self.extract_browse_btn.pack(side="right", padx=(5, 10), pady=10)
        
        # 输出目录框架
        output_frame = ctk.CTkFrame(self.extract_tab)
        output_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(output_frame, text="输出目录:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        output_select_frame = ctk.CTkFrame(output_frame)
        output_select_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        self.extract_output_entry = ctk.CTkEntry(output_select_frame, placeholder_text="选择解压输出目录...")
        self.extract_output_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)
        
        self.extract_output_btn = ctk.CTkButton(
            output_select_frame,
            text="浏览",
            width=80,
            command=self.browse_extract_output
        )
        self.extract_output_btn.pack(side="right", padx=(5, 10), pady=10)
        
        # 文件信息框架
        info_frame = ctk.CTkFrame(self.extract_tab)
        info_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(info_frame, text="文件信息:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        self.extract_info_text = ctk.CTkTextbox(info_frame, height=80)
        self.extract_info_text.pack(fill="x", padx=10, pady=(0, 10))
        self.extract_info_text.insert("0.0", "未选择文件")
        
        # 操作按钮
        button_frame = ctk.CTkFrame(self.extract_tab)
        button_frame.pack(fill="x", padx=20, pady=10)
        
        self.extract_btn = ctk.CTkButton(
            button_frame,
            text="🚀 开始解包",
            font=ctk.CTkFont(size=16, weight="bold"),
            height=40,
            command=self.start_extract
        )
        self.extract_btn.pack(side="left", padx=10, pady=10)
        
        self.open_extract_btn = ctk.CTkButton(
            button_frame,
            text="📂 打开目录",
            height=40,
            state="disabled",
            command=self.open_extract_directory
        )
        self.open_extract_btn.pack(side="left", padx=10, pady=10)
        
        # 进度条
        self.extract_progress = ctk.CTkProgressBar(self.extract_tab)
        self.extract_progress.pack(fill="x", padx=20, pady=10)
        self.extract_progress.set(0)
        
        # 日志框架
        log_frame = ctk.CTkFrame(self.extract_tab)
        log_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        ctk.CTkLabel(log_frame, text="操作日志:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        self.extract_log = ctk.CTkTextbox(log_frame)
        self.extract_log.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
    def setup_pack_tab(self):
        """设置打包选项卡"""
        # 目录选择框架
        dir_frame = ctk.CTkFrame(self.pack_tab)
        dir_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(dir_frame, text="选择要打包的目录:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        dir_select_frame = ctk.CTkFrame(dir_frame)
        dir_select_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        self.pack_dir_entry = ctk.CTkEntry(dir_select_frame, placeholder_text="请选择要打包的目录...")
        self.pack_dir_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)
        
        self.pack_browse_btn = ctk.CTkButton(
            dir_select_frame,
            text="浏览",
            width=80,
            command=self.browse_pack_directory
        )
        self.pack_browse_btn.pack(side="right", padx=(5, 10), pady=10)
        
        # 输出文件框架
        output_frame = ctk.CTkFrame(self.pack_tab)
        output_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(output_frame, text="输出文件:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        output_select_frame = ctk.CTkFrame(output_frame)
        output_select_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        self.pack_output_entry = ctk.CTkEntry(output_select_frame, placeholder_text="选择输出ramdisk文件...")
        self.pack_output_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)
        
        self.pack_output_btn = ctk.CTkButton(
            output_select_frame,
            text="浏览",
            width=80,
            command=self.browse_pack_output
        )
        self.pack_output_btn.pack(side="right", padx=(5, 10), pady=10)
        
        # 目录信息框架
        info_frame = ctk.CTkFrame(self.pack_tab)
        info_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(info_frame, text="目录信息:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        self.pack_info_text = ctk.CTkTextbox(info_frame, height=80)
        self.pack_info_text.pack(fill="x", padx=10, pady=(0, 10))
        self.pack_info_text.insert("0.0", "未选择目录")
        
        # 操作按钮
        button_frame = ctk.CTkFrame(self.pack_tab)
        button_frame.pack(fill="x", padx=20, pady=10)
        
        self.pack_btn = ctk.CTkButton(
            button_frame,
            text="📦 开始打包",
            font=ctk.CTkFont(size=16, weight="bold"),
            height=40,
            command=self.start_pack
        )
        self.pack_btn.pack(side="left", padx=10, pady=10)
        
        self.open_pack_btn = ctk.CTkButton(
            button_frame,
            text="📁 打开文件",
            height=40,
            state="disabled",
            command=self.open_pack_file
        )
        self.open_pack_btn.pack(side="left", padx=10, pady=10)
        
        # 进度条
        self.pack_progress = ctk.CTkProgressBar(self.pack_tab)
        self.pack_progress.pack(fill="x", padx=20, pady=10)
        self.pack_progress.set(0)
        
        # 日志框架
        log_frame = ctk.CTkFrame(self.pack_tab)
        log_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        ctk.CTkLabel(log_frame, text="操作日志:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        self.pack_log = ctk.CTkTextbox(log_frame)
        self.pack_log.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
    def setup_about_tab(self):
        """设置关于选项卡"""
        about_frame = ctk.CTkFrame(self.about_tab)
        about_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title = ctk.CTkLabel(
            about_frame,
            text="🔧 Ramdisk 解包打包工具",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title.pack(pady=20)
        
        # 版本信息
        version = ctk.CTkLabel(
            about_frame,
            text="版本 1.0.0",
            font=ctk.CTkFont(size=16)
        )
        version.pack(pady=5)
        
        # 作者信息
        author = ctk.CTkLabel(
            about_frame,
            text="By.举个🌰",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        author.pack(pady=5)
        
        # 功能说明
        features_text = """🎯 主要功能:
• 解包gzip压缩的CPIO格式ramdisk文件
• 重新打包目录为ramdisk文件
• 支持Android设备的根文件系统修改
• 现代化的用户界面

📋 支持格式:
• gzip + CPIO newc格式
• Android ramdisk镜像
• 自动识别文件格式

⚡ 特色功能:
• 实时进度显示
• 详细操作日志
• 文件信息分析
• 一键打开目录/文件"""
        
        features = ctk.CTkLabel(
            about_frame,
            text=features_text,
            font=ctk.CTkFont(size=14),
            justify="left"
        )
        features.pack(pady=20)
        
        # 版权信息
        copyright_text = ctk.CTkLabel(
            about_frame,
            text="© 2025 举个🌰 - 保留所有权利",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        copyright_text.pack(side="bottom", pady=10)

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.2f} {size_names[i]}"

    def analyze_file_info(self, file_path):
        """分析文件信息"""
        try:
            stat_info = os.stat(file_path)
            file_size = stat_info.st_size

            # 读取文件头
            with open(file_path, 'rb') as f:
                header = f.read(16)

            # 判断文件类型
            if header.startswith(b'\x1f\x8b'):
                file_type = "gzip压缩文件"
                magic = "1f8b"
            elif header.startswith(b'070701'):
                file_type = "CPIO newc格式"
                magic = "070701"
            elif header.startswith(b'070702'):
                file_type = "CPIO newc CRC格式"
                magic = "070702"
            else:
                file_type = "未知格式"
                magic = header[:4].hex()

            info_text = f"""文件路径: {file_path}
文件大小: {self.format_file_size(file_size)} ({file_size:,} 字节)
文件类型: {file_type}
魔数: {magic}
修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat_info.st_mtime))}"""

            return info_text

        except Exception as e:
            return f"文件分析失败: {str(e)}"

    def analyze_directory_info(self, dir_path):
        """分析目录信息"""
        try:
            file_count = 0
            dir_count = 0
            total_size = 0

            for root, dirs, files in os.walk(dir_path):
                dir_count += len(dirs)
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        file_size = os.path.getsize(file_path)
                        total_size += file_size
                        file_count += 1
                    except:
                        pass

            info_text = f"""目录路径: {dir_path}
文件数量: {file_count} 个
目录数量: {dir_count} 个
总大小: {self.format_file_size(total_size)} ({total_size:,} 字节)
修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(os.path.getmtime(dir_path)))}"""

            return info_text

        except Exception as e:
            return f"目录分析失败: {str(e)}"

    def log_message(self, log_widget, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_widget.insert("end", f"[{timestamp}] {message}\n")
        log_widget.see("end")
        self.root.update_idletasks()

    def browse_extract_file(self):
        """浏览选择解包文件"""
        file_path = filedialog.askopenfilename(
            title="选择Ramdisk文件",
            filetypes=[
                ("所有文件", "*.*"),
                ("Ramdisk文件", "ramdisk*"),
                ("镜像文件", "*.img"),
                ("压缩文件", "*.gz")
            ]
        )

        if file_path:
            self.extract_file_entry.delete(0, "end")
            self.extract_file_entry.insert(0, file_path)

            # 分析并显示文件信息
            file_info = self.analyze_file_info(file_path)
            self.extract_info_text.delete("0.0", "end")
            self.extract_info_text.insert("0.0", file_info)

            # 自动设置输出目录
            base_dir = os.path.dirname(file_path)
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_dir = os.path.join(base_dir, f"{base_name}_extracted")
            self.extract_output_entry.delete(0, "end")
            self.extract_output_entry.insert(0, output_dir)

            self.log_message(self.extract_log, f"📄 已选择文件: {os.path.basename(file_path)}")
            self.log_message(self.extract_log, f"📊 文件大小: {self.format_file_size(os.path.getsize(file_path))}")

    def browse_extract_output(self):
        """浏览选择解包输出目录"""
        dir_path = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.extract_output_entry.get() or os.getcwd()
        )

        if dir_path:
            self.extract_output_entry.delete(0, "end")
            self.extract_output_entry.insert(0, dir_path)
            self.log_message(self.extract_log, f"📂 输出目录: {dir_path}")

    def browse_pack_directory(self):
        """浏览选择打包目录"""
        dir_path = filedialog.askdirectory(
            title="选择要打包的目录"
        )

        if dir_path:
            self.pack_dir_entry.delete(0, "end")
            self.pack_dir_entry.insert(0, dir_path)

            # 分析并显示目录信息
            dir_info = self.analyze_directory_info(dir_path)
            self.pack_info_text.delete("0.0", "end")
            self.pack_info_text.insert("0.0", dir_info)

            # 自动设置输出文件
            base_dir = os.path.dirname(dir_path)
            dir_name = os.path.basename(dir_path)
            output_file = os.path.join(base_dir, f"{dir_name}_packed")
            self.pack_output_entry.delete(0, "end")
            self.pack_output_entry.insert(0, output_file)

            self.log_message(self.pack_log, f"📁 已选择目录: {os.path.basename(dir_path)}")

    def browse_pack_output(self):
        """浏览选择打包输出文件"""
        file_path = filedialog.asksaveasfilename(
            title="选择输出文件",
            defaultextension="",
            filetypes=[
                ("所有文件", "*.*"),
                ("Ramdisk文件", "ramdisk*"),
                ("镜像文件", "*.img")
            ]
        )

        if file_path:
            self.pack_output_entry.delete(0, "end")
            self.pack_output_entry.insert(0, file_path)
            self.log_message(self.pack_log, f"💾 输出文件: {file_path}")

    def start_extract(self):
        """开始解包操作"""
        ramdisk_file = self.extract_file_entry.get().strip()
        output_dir = self.extract_output_entry.get().strip()

        if not ramdisk_file:
            messagebox.showwarning("警告", "请选择ramdisk文件！")
            return

        if not os.path.exists(ramdisk_file):
            messagebox.showwarning("警告", "选择的文件不存在！")
            return

        if not output_dir:
            messagebox.showwarning("警告", "请选择输出目录！")
            return

        # 禁用按钮，重置进度条
        self.extract_btn.configure(state="disabled")
        self.extract_progress.set(0)
        self.open_extract_btn.configure(state="disabled")

        self.log_message(self.extract_log, "=" * 50)
        self.log_message(self.extract_log, "🚀 开始解包操作...")

        # 启动工作线程
        self.current_operation = "extract"
        thread = threading.Thread(target=self.extract_worker, args=(ramdisk_file, output_dir))
        thread.daemon = True
        thread.start()

    def extract_worker(self, ramdisk_file, output_dir):
        """解包工作线程"""
        try:
            self.log_message(self.extract_log, "🔍 正在分析文件格式...")
            self.extract_progress.set(0.1)

            # 检查文件格式
            with open(ramdisk_file, 'rb') as f:
                header = f.read(16)

            if header.startswith(b'\x1f\x8b'):
                file_type = 'gzip'
            else:
                self.log_message(self.extract_log, "❌ 不支持的文件格式")
                self.extract_finished(False, "不支持的文件格式")
                return

            self.log_message(self.extract_log, f"✅ 检测到文件格式: {file_type}")
            self.extract_progress.set(0.2)

            # 解压gzip
            self.log_message(self.extract_log, "📦 正在解压gzip文件...")
            temp_file = ramdisk_file + "_temp_ungzip"

            with gzip.open(ramdisk_file, 'rb') as f_in:
                with open(temp_file, 'wb') as f_out:
                    f_out.write(f_in.read())

            self.extract_progress.set(0.4)

            # 解压CPIO
            self.log_message(self.extract_log, "📂 正在解压CPIO文件...")
            success = self.extract_cpio(temp_file, output_dir)

            # 清理临时文件
            try:
                os.remove(temp_file)
            except:
                pass

            if success:
                self.extract_progress.set(1.0)
                self.extract_finished(True, f"解包完成！文件保存在: {output_dir}")
            else:
                self.extract_finished(False, "CPIO解压失败")

        except Exception as e:
            self.extract_finished(False, f"解包过程中出错: {str(e)}")

    def extract_cpio(self, input_file, output_dir):
        """解压CPIO文件"""
        try:
            os.makedirs(output_dir, exist_ok=True)

            with open(input_file, 'rb') as f:
                data = f.read()

            offset = 0
            file_count = 0

            while offset < len(data):
                # 更新进度
                progress = 0.4 + (offset / len(data)) * 0.5
                self.extract_progress.set(progress)

                header, new_offset = self.cpio_handler.parse_newc_header(data, offset)
                if header is None:
                    break

                offset = new_offset

                if offset + header['namesize'] > len(data):
                    break

                filename_data = data[offset:offset + header['namesize']]
                filename = filename_data.rstrip(b'\x00').decode('utf-8', errors='ignore')
                offset += header['namesize']
                offset = (offset + 3) & ~3

                if filename == 'TRAILER!!!':
                    break

                full_path = os.path.join(output_dir, filename.lstrip('/'))
                dir_path = os.path.dirname(full_path)

                if dir_path:
                    os.makedirs(dir_path, exist_ok=True)

                file_mode = header['mode']

                if stat.S_ISDIR(file_mode):
                    os.makedirs(full_path, exist_ok=True)
                elif stat.S_ISREG(file_mode):
                    if offset + header['filesize'] > len(data):
                        break

                    file_data = data[offset:offset + header['filesize']]

                    try:
                        with open(full_path, 'wb') as out_file:
                            out_file.write(file_data)
                        file_count += 1
                    except Exception as e:
                        self.log_message(self.extract_log, f"⚠️ 写入文件失败 {filename}: {e}")

                    offset += header['filesize']
                elif stat.S_ISLNK(file_mode):
                    if offset + header['filesize'] > len(data):
                        break

                    link_target = data[offset:offset + header['filesize']].decode('utf-8', errors='ignore')

                    try:
                        with open(full_path + '.symlink', 'w') as f:
                            f.write(f"SYMLINK_TARGET: {link_target}\n")
                    except:
                        pass

                    offset += header['filesize']
                else:
                    offset += header['filesize']

                offset = (offset + 3) & ~3

            self.log_message(self.extract_log, f"✅ 解压完成，共处理 {file_count} 个文件")
            return True

        except Exception as e:
            self.log_message(self.extract_log, f"❌ CPIO解压错误: {str(e)}")
            return False

    def extract_finished(self, success, message):
        """解包完成回调"""
        self.extract_btn.configure(state="normal")

        if success:
            self.log_message(self.extract_log, "🎉 " + message)
            self.open_extract_btn.configure(state="normal")
            messagebox.showinfo("解包成功", message)
        else:
            self.log_message(self.extract_log, "❌ " + message)
            messagebox.showerror("解包失败", message)

    def start_pack(self):
        """开始打包操作"""
        pack_dir = self.pack_dir_entry.get().strip()
        output_file = self.pack_output_entry.get().strip()

        if not pack_dir:
            messagebox.showwarning("警告", "请选择要打包的目录！")
            return

        if not os.path.exists(pack_dir):
            messagebox.showwarning("警告", "选择的目录不存在！")
            return

        if not output_file:
            messagebox.showwarning("警告", "请选择输出文件！")
            return

        # 禁用按钮，重置进度条
        self.pack_btn.configure(state="disabled")
        self.pack_progress.set(0)
        self.open_pack_btn.configure(state="disabled")

        self.log_message(self.pack_log, "=" * 50)
        self.log_message(self.pack_log, "📦 开始打包操作...")

        # 启动工作线程
        self.current_operation = "pack"
        thread = threading.Thread(target=self.pack_worker, args=(pack_dir, output_file))
        thread.daemon = True
        thread.start()

    def pack_worker(self, pack_dir, output_file):
        """打包工作线程"""
        try:
            self.log_message(self.pack_log, "📊 正在分析目录结构...")
            self.pack_progress.set(0.1)

            # 创建临时CPIO文件
            temp_cpio = output_file + "_temp.cpio"

            self.log_message(self.pack_log, "📦 正在打包为CPIO格式...")
            success = self.pack_cpio(pack_dir, temp_cpio)

            if not success:
                self.pack_finished(False, "CPIO打包失败")
                return

            self.pack_progress.set(0.8)

            # 压缩为gzip
            self.log_message(self.pack_log, "🗜️ 正在压缩为gzip格式...")
            success = self.compress_gzip(temp_cpio, output_file)

            # 清理临时文件
            try:
                os.remove(temp_cpio)
            except:
                pass

            if success:
                self.pack_progress.set(1.0)
                file_size = os.path.getsize(output_file)
                self.pack_finished(True, f"打包完成！文件保存为: {output_file}\n文件大小: {self.format_file_size(file_size)}")
            else:
                self.pack_finished(False, "gzip压缩失败")

        except Exception as e:
            self.pack_finished(False, f"打包过程中出错: {str(e)}")

    def pack_cpio(self, input_dir, output_file):
        """将目录打包为CPIO格式"""
        try:
            file_list = []
            ino_counter = 1

            # 收集所有文件和目录
            for root, dirs, files in os.walk(input_dir):
                # 添加目录
                for dirname in sorted(dirs):
                    dir_path = os.path.join(root, dirname)
                    rel_path = os.path.relpath(dir_path, input_dir).replace('\\', '/')

                    try:
                        dir_stat = os.stat(dir_path)
                        file_list.append({
                            'path': rel_path,
                            'full_path': dir_path,
                            'mode': stat.S_IFDIR | 0o755,
                            'size': 0,
                            'mtime': int(dir_stat.st_mtime),
                            'ino': ino_counter,
                            'type': 'dir'
                        })
                        ino_counter += 1
                    except Exception as e:
                        self.log_message(self.pack_log, f"⚠️ 无法获取目录状态 {dir_path}: {e}")

                # 添加文件
                for filename in sorted(files):
                    file_path = os.path.join(root, filename)
                    rel_path = os.path.relpath(file_path, input_dir).replace('\\', '/')

                    # 跳过符号链接标记文件
                    if filename.endswith('.symlink'):
                        continue

                    try:
                        file_stat = os.stat(file_path)
                        file_list.append({
                            'path': rel_path,
                            'full_path': file_path,
                            'mode': stat.S_IFREG | 0o644,
                            'size': file_stat.st_size,
                            'mtime': int(file_stat.st_mtime),
                            'ino': ino_counter,
                            'type': 'file'
                        })
                        ino_counter += 1
                    except Exception as e:
                        self.log_message(self.pack_log, f"⚠️ 无法获取文件状态 {file_path}: {e}")

            # 按路径排序
            file_list.sort(key=lambda x: x['path'])

            self.log_message(self.pack_log, f"📋 共找到 {len(file_list)} 个项目")

            # 写入CPIO文件
            with open(output_file, 'wb') as f:
                for i, item in enumerate(file_list):
                    # 更新进度
                    progress = 0.2 + (i / len(file_list)) * 0.5
                    self.pack_progress.set(progress)

                    # 创建头部
                    header = self.cpio_handler.create_header(
                        item['path'],
                        item['mode'],
                        item['size'],
                        item['mtime'],
                        item['ino']
                    )
                    f.write(header)

                    # 写入文件名
                    filename_data = (item['path'] + '\x00').encode('utf-8')
                    f.write(self.cpio_handler.pad_to_4_bytes(filename_data))

                    # 写入文件内容
                    if item['type'] == 'file':
                        try:
                            with open(item['full_path'], 'rb') as file_in:
                                file_data = file_in.read()
                                f.write(self.cpio_handler.pad_to_4_bytes(file_data))
                        except Exception as e:
                            self.log_message(self.pack_log, f"⚠️ 无法读取文件 {item['full_path']}: {e}")

                # 写入结束标记
                trailer_header = self.cpio_handler.create_header('TRAILER!!!', 0, 0, int(time.time()), ino_counter)
                f.write(trailer_header)
                trailer_name = b'TRAILER!!!\x00'
                f.write(self.cpio_handler.pad_to_4_bytes(trailer_name))

            self.log_message(self.pack_log, f"✅ CPIO打包完成: {output_file}")
            return True

        except Exception as e:
            self.log_message(self.pack_log, f"❌ CPIO打包错误: {str(e)}")
            return False

    def compress_gzip(self, input_file, output_file):
        """使用gzip压缩文件"""
        try:
            with open(input_file, 'rb') as f_in:
                with gzip.open(output_file, 'wb') as f_out:
                    f_out.write(f_in.read())

            self.log_message(self.pack_log, f"✅ gzip压缩完成: {output_file}")
            return True
        except Exception as e:
            self.log_message(self.pack_log, f"❌ gzip压缩失败: {str(e)}")
            return False

    def pack_finished(self, success, message):
        """打包完成回调"""
        self.pack_btn.configure(state="normal")

        if success:
            self.log_message(self.pack_log, "🎉 " + message)
            self.open_pack_btn.configure(state="normal")
            messagebox.showinfo("打包成功", message)
        else:
            self.log_message(self.pack_log, "❌ " + message)
            messagebox.showerror("打包失败", message)

    def open_extract_directory(self):
        """打开解包输出目录"""
        output_dir = self.extract_output_entry.get().strip()
        if output_dir and os.path.exists(output_dir):
            try:
                os.startfile(output_dir)
                self.log_message(self.extract_log, f"📂 已打开目录: {output_dir}")
            except Exception as e:
                self.log_message(self.extract_log, f"❌ 打开目录失败: {str(e)}")
                messagebox.showerror("错误", f"无法打开目录: {str(e)}")
        else:
            messagebox.showwarning("警告", "目录不存在或路径为空！")

    def open_pack_file(self):
        """打开打包输出文件所在目录"""
        output_file = self.pack_output_entry.get().strip()
        if output_file and os.path.exists(output_file):
            try:
                # 打开文件所在目录并选中文件
                os.system(f'explorer /select,"{output_file}"')
                self.log_message(self.pack_log, f"📁 已打开文件位置: {output_file}")
            except Exception as e:
                self.log_message(self.pack_log, f"❌ 打开文件失败: {str(e)}")
                messagebox.showerror("错误", f"无法打开文件: {str(e)}")
        else:
            messagebox.showwarning("警告", "文件不存在或路径为空！")

    def run(self):
        """运行应用程序"""
        # 初始化日志
        self.log_message(self.extract_log, "🎉 欢迎使用Ramdisk解包打包工具！")
        self.log_message(self.extract_log, "📁 请选择要解包的ramdisk文件...")
        self.log_message(self.extract_log, "💡 支持gzip压缩的CPIO格式ramdisk文件")

        self.log_message(self.pack_log, "🎉 欢迎使用Ramdisk解包打包工具！")
        self.log_message(self.pack_log, "📁 请选择要打包的目录...")
        self.log_message(self.pack_log, "💡 将目录打包为gzip压缩的CPIO格式ramdisk文件")

        # 启动主循环
        self.root.mainloop()

def main():
    """主程序入口"""
    try:
        app = RamdiskTool()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        messagebox.showerror("错误", f"程序运行出错: {str(e)}")

if __name__ == "__main__":
    main()
