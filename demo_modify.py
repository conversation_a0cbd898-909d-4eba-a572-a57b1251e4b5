#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示如何修改ramdisk文件
By.举个🌰
"""

import os

def modify_build_prop(prop_file, modifications):
    """修改build.prop文件"""
    print(f"修改文件: {prop_file}")
    
    if not os.path.exists(prop_file):
        print(f"文件不存在: {prop_file}")
        return False
    
    # 读取原文件
    with open(prop_file, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()
    
    # 应用修改
    modified_lines = []
    modified_props = set()
    
    for line in lines:
        line = line.rstrip('\n\r')
        modified = False
        
        for prop_name, new_value in modifications.items():
            if line.startswith(f"{prop_name}=") or line.startswith(f"#{prop_name}="):
                modified_lines.append(f"{prop_name}={new_value}\n")
                modified_props.add(prop_name)
                modified = True
                print(f"  修改: {prop_name}={new_value}")
                break
        
        if not modified:
            modified_lines.append(line + '\n')
    
    # 添加新属性（如果不存在）
    for prop_name, new_value in modifications.items():
        if prop_name not in modified_props:
            modified_lines.append(f"{prop_name}={new_value}\n")
            print(f"  添加: {prop_name}={new_value}")
    
    # 写回文件
    with open(prop_file, 'w', encoding='utf-8') as f:
        f.writelines(modified_lines)
    
    print(f"文件修改完成: {prop_file}")
    return True

def main():
    extracted_dir = "ramdisk_extracted"
    
    if not os.path.exists(extracted_dir):
        print(f"错误: 找不到解压目录 {extracted_dir}")
        return
    
    print("=== 演示修改ramdisk文件 ===")
    
    # 示例：修改一些属性以启用调试功能
    modifications = {
        'ro.debuggable': '1',           # 启用调试
        'ro.secure': '0',               # 禁用安全模式
        'ro.adb.secure': '0',           # 禁用ADB安全验证
        'persist.service.adb.enable': '1',  # 启用ADB服务
        'persist.service.debuggable': '1',  # 启用调试服务
        'persist.sys.usb.config': 'adb'     # USB配置为ADB模式
    }
    
    print("将要应用的修改:")
    for prop, value in modifications.items():
        print(f"  {prop} = {value}")
    
    # 修改所有build.prop文件
    modified_count = 0
    for filename in os.listdir(extracted_dir):
        if filename.startswith('build_') and filename.endswith('.prop'):
            prop_file = os.path.join(extracted_dir, filename)
            if modify_build_prop(prop_file, modifications):
                modified_count += 1
    
    print(f"\n✅ 成功修改了 {modified_count} 个build.prop文件")
    
    # 也可以修改init.rc文件
    init_rc_path = os.path.join(extracted_dir, 'init.rc')
    if os.path.exists(init_rc_path):
        print(f"\n发现init.rc文件: {init_rc_path}")
        print("你可以手动编辑这个文件来修改启动脚本")
    
    print("\n📝 修改完成！你现在可以:")
    print("1. 手动编辑 ramdisk_extracted 目录中的任何文件")
    print("2. 运行重新打包脚本来创建新的ramdisk")

if __name__ == "__main__":
    main()
