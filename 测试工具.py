#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ramdisk工具测试脚本
用于验证解包和打包功能
By.举个🌰
"""

import os
import sys
import time
import subprocess

def test_ramdisk_tool():
    """测试Ramdisk工具功能"""
    print("🔧 Ramdisk工具功能测试")
    print("=" * 50)
    
    # 检查原始ramdisk文件
    original_ramdisk = "ramdisk"
    if not os.path.exists(original_ramdisk):
        print("❌ 找不到原始ramdisk文件")
        return False
    
    print(f"✅ 找到原始ramdisk文件: {original_ramdisk}")
    original_size = os.path.getsize(original_ramdisk)
    print(f"📊 原始文件大小: {original_size:,} 字节")
    
    # 检查解包结果
    extracted_dir = "ramdisk_extracted"
    if os.path.exists(extracted_dir):
        print(f"✅ 找到解包目录: {extracted_dir}")
        
        # 统计解包文件
        file_count = 0
        dir_count = 0
        total_size = 0
        
        for root, dirs, files in os.walk(extracted_dir):
            dir_count += len(dirs)
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    file_size = os.path.getsize(file_path)
                    total_size += file_size
                    file_count += 1
                except:
                    pass
        
        print(f"📁 解包统计:")
        print(f"   - 文件数量: {file_count}")
        print(f"   - 目录数量: {dir_count}")
        print(f"   - 总大小: {total_size:,} 字节")
        
        # 检查关键文件
        key_files = [
            "init",
            "init.rc", 
            "sepolicy",
            "build_CM311-1e.prop"
        ]
        
        print(f"🔍 检查关键文件:")
        for key_file in key_files:
            file_path = os.path.join(extracted_dir, key_file)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"   ✅ {key_file}: {size:,} 字节")
            else:
                print(f"   ❌ {key_file}: 未找到")
    else:
        print(f"❌ 未找到解包目录: {extracted_dir}")
    
    # 检查重新打包的文件
    repacked_ramdisk = "ramdisk_modified"
    if os.path.exists(repacked_ramdisk):
        print(f"✅ 找到重新打包文件: {repacked_ramdisk}")
        repacked_size = os.path.getsize(repacked_ramdisk)
        print(f"📊 重新打包文件大小: {repacked_size:,} 字节")
        
        size_diff = repacked_size - original_size
        print(f"📈 大小差异: {size_diff:+,} 字节")
        
        if abs(size_diff) < original_size * 0.1:  # 差异小于10%
            print("✅ 文件大小差异在合理范围内")
        else:
            print("⚠️ 文件大小差异较大，请检查")
    else:
        print(f"❌ 未找到重新打包文件: {repacked_ramdisk}")
    
    print("\n" + "=" * 50)
    print("🎯 测试建议:")
    print("1. 使用GUI工具选择ramdisk文件进行解包")
    print("2. 修改解包后的文件（如build.prop）")
    print("3. 使用GUI工具重新打包修改后的目录")
    print("4. 比较原始文件和新文件的大小")
    
    return True

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    try:
        import customtkinter
        print("✅ CustomTkinter 已安装")
    except ImportError:
        print("❌ CustomTkinter 未安装，请运行: pip install customtkinter")
        return False
    
    return True

def main():
    """主函数"""
    print("🎉 Ramdisk工具测试程序")
    print("By.举个🌰")
    print()
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 运行测试
    test_ramdisk_tool()
    
    print("\n💡 提示:")
    print("- 运行 'python Ramdisk工具.py' 启动GUI工具")
    print("- 查看 'Ramdisk工具使用说明.md' 了解详细使用方法")

if __name__ == "__main__":
    main()
