#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ramdisk 解包工具 - GUI版本
基于PySide6 + QtModern风格
By.举个🌰
"""

import sys
import os
import gzip
import struct
import stat
import time
import threading
from pathlib import Path

try:
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *
    import qtmodern.styles
    import qtmodern.windows
except ImportError as e:
    print("缺少必要的依赖包，请运行以下命令安装：")
    print("pip install PySide6 qtmodern")
    sys.exit(1)

class CPIOExtractor:
    def __init__(self):
        self.MAGIC_NEWC = b'070701'
        self.MAGIC_CRC = b'070702'
        
    def parse_newc_header(self, data, offset):
        """解析 CPIO newc 格式头部"""
        if offset + 110 > len(data):
            return None, offset
            
        header_data = data[offset:offset + 110]
        
        if not header_data.startswith(self.MAGIC_NEWC) and not header_data.startswith(self.MAGIC_CRC):
            return None, offset
            
        try:
            magic = header_data[0:6].decode('ascii')
            ino = int(header_data[6:14], 16)
            mode = int(header_data[14:22], 16)
            uid = int(header_data[22:30], 16)
            gid = int(header_data[30:38], 16)
            nlink = int(header_data[38:46], 16)
            mtime = int(header_data[46:54], 16)
            filesize = int(header_data[54:62], 16)
            devmajor = int(header_data[62:70], 16)
            devminor = int(header_data[70:78], 16)
            rdevmajor = int(header_data[78:86], 16)
            rdevminor = int(header_data[86:94], 16)
            namesize = int(header_data[94:102], 16)
            check = int(header_data[102:110], 16)
            
            header = {
                'magic': magic,
                'ino': ino,
                'mode': mode,
                'uid': uid,
                'gid': gid,
                'nlink': nlink,
                'mtime': mtime,
                'filesize': filesize,
                'devmajor': devmajor,
                'devminor': devminor,
                'rdevmajor': rdevmajor,
                'rdevminor': rdevminor,
                'namesize': namesize,
                'check': check
            }
            
            return header, offset + 110
            
        except ValueError as e:
            return None, offset

class WorkerThread(QThread):
    progress_updated = Signal(int)
    status_updated = Signal(str)
    finished_signal = Signal(bool, str)
    
    def __init__(self, ramdisk_file, output_dir):
        super().__init__()
        self.ramdisk_file = ramdisk_file
        self.output_dir = output_dir
        self.extractor = CPIOExtractor()
        
    def run(self):
        try:
            self.status_updated.emit("正在分析文件格式...")
            self.progress_updated.emit(10)
            
            # 检查文件格式
            with open(self.ramdisk_file, 'rb') as f:
                header = f.read(16)
            
            if header.startswith(b'\x1f\x8b'):
                file_type = 'gzip'
            else:
                self.finished_signal.emit(False, "不支持的文件格式")
                return
            
            self.status_updated.emit(f"检测到文件格式: {file_type}")
            self.progress_updated.emit(20)
            
            # 解压gzip
            self.status_updated.emit("正在解压gzip文件...")
            temp_file = self.ramdisk_file + "_temp_ungzip"
            
            with gzip.open(self.ramdisk_file, 'rb') as f_in:
                with open(temp_file, 'wb') as f_out:
                    f_out.write(f_in.read())
            
            self.progress_updated.emit(40)
            
            # 解压CPIO
            self.status_updated.emit("正在解压CPIO文件...")
            success = self.extract_cpio(temp_file, self.output_dir)
            
            # 清理临时文件
            try:
                os.remove(temp_file)
            except:
                pass
            
            if success:
                self.progress_updated.emit(100)
                self.finished_signal.emit(True, f"解压完成！文件保存在: {self.output_dir}")
            else:
                self.finished_signal.emit(False, "CPIO解压失败")
                
        except Exception as e:
            self.finished_signal.emit(False, f"解压过程中出错: {str(e)}")
    
    def extract_cpio(self, input_file, output_dir):
        """解压CPIO文件"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            with open(input_file, 'rb') as f:
                data = f.read()
            
            offset = 0
            file_count = 0
            total_progress = 60  # 从60%开始到90%
            
            while offset < len(data):
                # 更新进度
                progress = 60 + int((offset / len(data)) * 30)
                self.progress_updated.emit(progress)
                
                header, new_offset = self.extractor.parse_newc_header(data, offset)
                if header is None:
                    break
                    
                offset = new_offset
                
                if offset + header['namesize'] > len(data):
                    break
                    
                filename_data = data[offset:offset + header['namesize']]
                filename = filename_data.rstrip(b'\x00').decode('utf-8', errors='ignore')
                offset += header['namesize']
                offset = (offset + 3) & ~3
                
                if filename == 'TRAILER!!!':
                    break
                
                full_path = os.path.join(output_dir, filename.lstrip('/'))
                dir_path = os.path.dirname(full_path)
                
                if dir_path:
                    os.makedirs(dir_path, exist_ok=True)
                
                file_mode = header['mode']
                
                if stat.S_ISDIR(file_mode):
                    os.makedirs(full_path, exist_ok=True)
                elif stat.S_ISREG(file_mode):
                    if offset + header['filesize'] > len(data):
                        break
                        
                    file_data = data[offset:offset + header['filesize']]
                    
                    try:
                        with open(full_path, 'wb') as out_file:
                            out_file.write(file_data)
                        file_count += 1
                    except Exception as e:
                        print(f"写入文件失败 {filename}: {e}")
                    
                    offset += header['filesize']
                elif stat.S_ISLNK(file_mode):
                    if offset + header['filesize'] > len(data):
                        break
                        
                    link_target = data[offset:offset + header['filesize']].decode('utf-8', errors='ignore')
                    
                    try:
                        with open(full_path + '.symlink', 'w') as f:
                            f.write(f"SYMLINK_TARGET: {link_target}\n")
                    except:
                        pass
                    
                    offset += header['filesize']
                else:
                    offset += header['filesize']
                
                offset = (offset + 3) & ~3
            
            self.status_updated.emit(f"解压完成，共处理 {file_count} 个文件")
            return True
            
        except Exception as e:
            self.status_updated.emit(f"CPIO解压错误: {str(e)}")
            return False

class RamdiskExtractorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.worker = None
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("Ramdisk 解包工具 - By.举个🌰")
        self.setMinimumSize(900, 700)
        self.setWindowIcon(self.create_icon())
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("Ramdisk 解包工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # 文件选择区域
        file_group = QGroupBox("选择Ramdisk文件")
        file_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        file_layout = QHBoxLayout(file_group)
        
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("请选择ramdisk文件...")
        self.file_path_edit.setMinimumHeight(35)
        
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.setMinimumSize(80, 35)
        self.browse_btn.clicked.connect(self.browse_file)
        
        file_layout.addWidget(self.file_path_edit)
        file_layout.addWidget(self.browse_btn)
        layout.addWidget(file_group)
        
        # 输出目录选择
        output_group = QGroupBox("输出目录")
        output_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        output_layout = QHBoxLayout(output_group)
        
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setPlaceholderText("选择解压输出目录...")
        self.output_path_edit.setMinimumHeight(35)
        
        self.output_browse_btn = QPushButton("浏览")
        self.output_browse_btn.setMinimumSize(80, 35)
        self.output_browse_btn.clicked.connect(self.browse_output_dir)
        
        output_layout.addWidget(self.output_path_edit)
        output_layout.addWidget(self.output_browse_btn)
        layout.addWidget(output_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.extract_btn = QPushButton("开始解包")
        self.extract_btn.setMinimumSize(120, 40)
        self.extract_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.extract_btn.clicked.connect(self.start_extraction)
        
        self.open_output_btn = QPushButton("打开输出目录")
        self.open_output_btn.setMinimumSize(120, 40)
        self.open_output_btn.setEnabled(False)
        self.open_output_btn.clicked.connect(self.open_output_directory)
        
        button_layout.addStretch()
        button_layout.addWidget(self.extract_btn)
        button_layout.addWidget(self.open_output_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimumHeight(25)
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态显示
        self.status_text = QTextEdit()
        self.status_text.setMinimumHeight(200)
        self.status_text.setReadOnly(True)
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.status_text)
        
        # 文件信息显示
        info_group = QGroupBox("文件信息")
        info_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        info_layout = QVBoxLayout(info_group)

        self.file_info_label = QLabel("未选择文件")
        self.file_info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
        """)
        info_layout.addWidget(self.file_info_label)
        layout.addWidget(info_group)

        # 版权信息
        copyright_label = QLabel("© 2025 By.举个🌰 - Ramdisk解包工具")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 11px;
                margin-top: 10px;
            }
        """)
        layout.addWidget(copyright_label)

        # 初始化状态
        self.add_status_message("🎉 欢迎使用Ramdisk解包工具！")
        self.add_status_message("📁 请选择要解包的ramdisk文件...")
        self.add_status_message("💡 支持gzip压缩的CPIO格式ramdisk文件")

    def create_icon(self):
        """创建应用图标"""
        pixmap = QPixmap(32, 32)
        pixmap.fill(QColor(52, 152, 219))

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "R")
        painter.end()

        return QIcon(pixmap)

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.2f} {size_names[i]}"

    def analyze_file_info(self, file_path):
        """分析文件信息"""
        try:
            stat_info = os.stat(file_path)
            file_size = stat_info.st_size

            # 读取文件头
            with open(file_path, 'rb') as f:
                header = f.read(16)

            # 判断文件类型
            if header.startswith(b'\x1f\x8b'):
                file_type = "gzip压缩文件"
                magic = "1f8b"
            elif header.startswith(b'070701'):
                file_type = "CPIO newc格式"
                magic = "070701"
            elif header.startswith(b'070702'):
                file_type = "CPIO newc CRC格式"
                magic = "070702"
            else:
                file_type = "未知格式"
                magic = header[:4].hex()

            info_text = f"""文件路径: {file_path}
文件大小: {self.format_file_size(file_size)} ({file_size:,} 字节)
文件类型: {file_type}
魔数: {magic}
修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat_info.st_mtime))}"""

            return info_text

        except Exception as e:
            return f"文件分析失败: {str(e)}"

    def browse_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Ramdisk文件",
            "",
            "所有文件 (*);;Ramdisk文件 (ramdisk*);;镜像文件 (*.img);;压缩文件 (*.gz)"
        )

        if file_path:
            self.file_path_edit.setText(file_path)

            # 分析并显示文件信息
            file_info = self.analyze_file_info(file_path)
            self.file_info_label.setText(file_info)

            # 自动设置输出目录
            base_dir = os.path.dirname(file_path)
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_dir = os.path.join(base_dir, f"{base_name}_extracted")
            self.output_path_edit.setText(output_dir)

            self.add_status_message(f"📄 已选择文件: {os.path.basename(file_path)}")
            self.add_status_message(f"📊 文件大小: {self.format_file_size(os.path.getsize(file_path))}")
            
    def browse_output_dir(self):
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择输出目录",
            self.output_path_edit.text() or os.getcwd()
        )

        if dir_path:
            self.output_path_edit.setText(dir_path)
            self.add_status_message(f"📂 输出目录: {dir_path}")

    def add_status_message(self, message):
        timestamp = time.strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        # 自动滚动到底部
        scrollbar = self.status_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def start_extraction(self):
        ramdisk_file = self.file_path_edit.text().strip()
        output_dir = self.output_path_edit.text().strip()
        
        if not ramdisk_file:
            QMessageBox.warning(self, "警告", "请选择ramdisk文件！")
            return
            
        if not os.path.exists(ramdisk_file):
            QMessageBox.warning(self, "警告", "选择的文件不存在！")
            return
            
        if not output_dir:
            QMessageBox.warning(self, "警告", "请选择输出目录！")
            return
        
        # 禁用按钮，显示进度条
        self.extract_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        self.add_status_message("=" * 50)
        self.add_status_message("开始解包操作...")
        
        # 启动工作线程
        self.worker = WorkerThread(ramdisk_file, output_dir)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.add_status_message)
        self.worker.finished_signal.connect(self.extraction_finished)
        self.worker.start()
    
    def extraction_finished(self, success, message):
        self.extract_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

        if success:
            self.add_status_message("✅ " + message)
            self.add_status_message("🎉 解包操作完成！")
            self.open_output_btn.setEnabled(True)

            # 显示成功对话框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("解包成功")
            msg_box.setText("Ramdisk解包完成！")
            msg_box.setInformativeText(message)
            msg_box.setIcon(QMessageBox.Information)
            msg_box.addButton("打开目录", QMessageBox.AcceptRole)
            msg_box.addButton("确定", QMessageBox.RejectRole)

            if msg_box.exec() == 0:  # 打开目录按钮
                self.open_output_directory()
        else:
            self.add_status_message("❌ " + message)
            self.add_status_message("💡 请检查文件格式是否正确")
            QMessageBox.critical(self, "解包失败", message)
    
    def open_output_directory(self):
        output_dir = self.output_path_edit.text().strip()
        if output_dir and os.path.exists(output_dir):
            os.startfile(output_dir)

def main():
    app = QApplication(sys.argv)
    
    # 应用QtModern样式
    qtmodern.styles.dark(app)
    
    # 创建主窗口
    window = RamdiskExtractorGUI()
    
    # 应用现代化窗口样式
    mw = qtmodern.windows.ModernWindow(window)
    mw.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
